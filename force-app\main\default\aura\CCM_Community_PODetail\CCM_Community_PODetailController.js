({
    doInit: function(component, event, helper){
        // 判断是否小铃铛
        if (window.location.pathname.indexOf('Purchase_Order__c/') > 0) {
            let recordId = window.location.pathname.slice(window.location.pathname.indexOf('Purchase_Order__c/') + 18, window.location.pathname.indexOf('Purchase_Order__c/') + 18 + 18);
            component.set('v.recordId', recordId);
        } else {
            // 判断url参数
            const params = new URLSearchParams(window.location.search);
            const recordId =  params.get('0.recordId');
            const type =  params.get('0.type');
            component.set('v.recordId', recordId);
        }
        component.set('v.currencySymbol', $A.get("$Locale.currencyCode"));
        // 进度条
        var pathDataForInsideSales = [
            {label: $A.get("$Label.c.Order_NewOrder"), icon: 'edit_form'},
            {label: $A.get("$Label.c.Order_PendingReview"), icon: 'locker_service_api_viewer'},
            {label: $A.get("$Label.c.Order_ReviewinProcess"), icon: 'entitlement'},
            {label: $A.get("$Label.c.CCM_OrderProcessing"), icon: 'privately_shared'},
            {label: $A.get("$Label.c.CCM_PartialShipment"), icon: 'travel_and_places'},
            {label: $A.get("$Label.c.CCM_ShipmentComplete"), icon:'success'}
        ];
        var pathDataForSalesRep = [
            {label: $A.get("$Label.c.Order_NewOrder"), icon: 'edit_form'},
            {label: $A.get("$Label.c.CCM_Submitted"), icon: 'locker_service_api_viewer'},
            {label: $A.get("$Label.c.Order_ReviewinProcess"), icon: 'entitlement'},
            {label: $A.get("$Label.c.CCM_OrderProcessing"), icon: 'privately_shared'},
            {label: $A.get("$Label.c.CCM_PartialShipment"), icon: 'travel_and_places'},
            {label: $A.get("$Label.c.CCM_ShipmentComplete"), icon:'success'}
        ];

        // attachment table
        component.set('v.attachmentColumns', [
            {   
                label: $A.get("$Label.c.CCM_Action"),
                width: '60px',
                tdStyle: 'text-align: center',
                children:[
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${contentId}",
                            variant:"bare",
                            iconName:"utility:preview",
                            alternativeText:"download",
                            onclick: component.getReference('c.doView')
                        }
                    },
                    {
                        type: "lightning:buttonIcon",
                        attributes:{
                            value: "${contentId}",
                            variant:"bare",
                            iconName:"utility:delete",
                            alternativeText:"delete",
                            onclick: component.getReference('c.doDelete')
                        }
                    },
                ]
            },
            {label: $A.get("$Label.c.CCM_AttachmentName"), fieldName: 'attachmentName'},
            {label: $A.get("$Label.c.CCM_AttachmentType"), fieldName: 'attachmentType'},
            {label: $A.get("$Label.c.CCM_Date"), fieldName:'attachmentDate'},
        ]);
        // attachmentTypeOptions
        component.set('v.attachmentTypeOptions', [
            {
                label: 'Profoma Invoice',
                value: 'Profoma Invoice',
            },
            {
                label: 'Packing List',
                value: 'Packing List',
            },
            {
                label: 'Purchase Order',
                value: 'Purchase Order',
            },
            {
                label: 'Others',
                value: 'Others',
            },
        ]);
        component.set('v.isBusy', true);
        var action = component.get("c.QueryPirchaseAndItemInfo");
        action.setParams({
            PurchaseOrderId: component.get('v.recordId'),
            IsProtal: false,
        });
        action.setCallback(this, function (response) {
            var state = response.getState(); 
            if (state === "SUCCESS") {
                var results = JSON.parse(response.getReturnValue());
                if(results){
                    component.set('v.currencySymbol', results.customerCurrencyISOCode);
                    // 流程条
                    if (results.UserType == 'InsideSales') {
                        component.set('v.processData', pathDataForInsideSales);
                    } else {
                        component.set('v.processData', pathDataForSalesRep);
                    }
                    component.set('v.userType', results.UserType);
                    component.set('v.currentStep', Number(results.CurrentStep) + 1);
                    component.set('v.basicInformation', results);
                    component.set('v.orderItemList', results.lstPurchaseOrderItem);
                    component.set('v.TotalDueAmount', results.TotalDueAmount);
                    component.set('v.CountLine', results.CountLine);
                    component.set('v.poStatus', results.Status);
                    if (results.lstAttachmentInfo.length) {
                        let attachmentList = [];
                        results.lstAttachmentInfo.forEach((attachmentItem)=>{
                            attachmentList.push({
                                contentId: attachmentItem.fileId,
                                attachmentName: attachmentItem.fileName,
                                attachmentType: attachmentItem.fileType,
                                attachmentDate: attachmentItem.fileDate,
                            })
                        });
                        component.set('v.attachment', [...attachmentList]);
                    }
                    if(!results.isSynced || results.syncStatus == 'Failed') {
                        component.set('v.notSync', true);
                    }
                    component.set('v.syncStatus', results.syncStatus);
                    component.set('v.syncMessage', results.syncMessage);
                    // alert message
                    component.set('v.isBusy', false);
                    if (component.get('v.userType') == 'InsideSales') {
                        helper.queryAlertMessage(component, results.CustomerNumber);
                    };
                }
            } else {
                var errors = response.getError();
                console.log('errors:',errors);
            };
            component.set('v.isBusy', false);
        });
        $A.enqueueAction(action);
    },
    doBack: function(component) {
        let url = window.location.origin + '/lightning/n/Purchase_Order_List';
        window.open(url, '_self');
    },
    doView: function(component, event, helper){
        var contentId = event.getSource().get('v.value');
        console.log(contentId, 'rowData--------------');
        // 打开预览模式
        var openPreview = $A.get('e.lightning:openFiles');
        openPreview.fire({
            // recordIds: ['0697Y000004hXuAQAU']
            recordIds: [contentId]
        });
    },
    // 从列表上删除附件
    doDelete : function(component, event, helper) {
        let contentId = event.getSource().get('v.value');
        let attachment = component.get('v.attachment');
        console.log(contentId, 'contentId----------');
        attachment = attachment.filter((item)=>{
            return item.contentId !== contentId;
        });
        component.set('v.attachment', attachment);
    },
    showToolList: function(component, event, helper){
        var id = event.currentTarget.getAttribute('id');
        var expanded = event.currentTarget.getAttribute('data-expanded');
        if(expanded == 'true'){
            document.getElementById('tool' + id).style.display = 'none'
            event.currentTarget.setAttribute('data-expanded', false);
        }else{
            document.getElementById('tool' + id).style.display = 'table-row';
            event.currentTarget.setAttribute('data-expanded', true);
        }
    },
    // 同步操作
    doSync: function(component, event, helper){
        // helper.syncEvent(component);
        helper.handleSync(component);
    },
    // inside 用户编辑操作
    doEdit: function(component, event, helper){
        var recordId = component.get('v.recordId');
        let url = window.location.origin + '/lightning/n/Delegate_Order?0.recordId=' + recordId + '&0.type=edit';
        window.open(url, '_self');
    },
    // 打开附件弹框
    uploadFileItem: function(component, event, helper){
        console.log('打开附件弹框-----------');
        component.set('v.uploadModalFlag', true);
    },
    // 关闭附件弹框
    cancelEvent: function(component, event, helper){
        component.set('v.uploadModalFlag', false);
        component.set('v.uploadFinished', false);
        component.set('v.attachmentName', '');
        component.set('v.attachmentType', '');
    },
    // 上传附件
    handleFilesChange : function(component, event, helper) {
        component.set('v.isBusy', true);
        var files = event.getSource().get("v.files");
        let uploadItem = component.get('v.attachmentItem');
        let attachmentName = component.get('v.attachmentName');
        console.log(files, 'file=======');
        // 显示附件信息
        if (files.length > 0) {
            component.set('v.uploadFinished', true);
        }
        // 附件赋值
        if (!attachmentName) {
            component.set('v.attachmentName', files[0].name);
        }
        uploadItem.name = files[0].name;
        component.set('v.attachmentItem', uploadItem);
        // 转base64
        helper.fileByBase64(files, component, attachmentName || files[0].name );
    },
    // 删除附件
    deleteAttachmentItem : function(component, event, helper) {
        component.set('v.uploadFinished', false);
        component.set('v.attachmentItem', {});
    },
    // 保存当前附件
    saveFileItem : function(component, event, helper) {
        let attachment = component.get('v.attachment');
        let attachmentItem = component.get('v.attachmentItem');
        let attachmentName = component.get('v.attachmentName');
        let attachmentType = component.get('v.attachmentType');
        console.log(JSON.parse(JSON.stringify(attachmentItem)), 'attachmentItem-------------');
        // 获取当前时间
        const year = new Date().getFullYear().toString();
        const month = (new Date().getMonth() + 1).toString().length < 2 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1;
        const day = new Date().getDate().toString().length < 2 ? '0' + new Date().getDate() : new Date().getDate();
        attachment.push({
            attachmentName: attachmentName,
            attachmentType: attachmentType,
            attachmentDate: `${year}-${month}-${day}`,
            contentId: attachmentItem.contentId,
        });
        // 关闭弹框
        component.set('v.attachment', attachment);
        component.set('v.uploadModalFlag', false);
        component.set('v.uploadFinished', false);
        component.set('v.attachmentName', '');
        component.set('v.attachmentType', '');
    },
    // 保存附件
    saveFileLIst : function(component, event, helper) {
        helper.uplaodFileEvent(component);
    },
    // 刷新红绿灯
    refreshTrafficLight : function(component, event, helper) {
        console.log('刷新红绿灯---------------');
        helper.refreshLight(component);
    },
    // 取消同步
    doCancelSync : function(component, event, helper) {
        helper.cancelSyncEvent(component);
    },
})