/**
 * author: Austin
 * Date:2023.07.03
 * Description: apex class for all lentout
 */
public without sharing class CCM_SalesRepStock_LentOut {
    // 系统生成的文件
    final static String strTypePrefixFromSys = 'sys-agreement-';
    // 人为上传的文件
    final static String strTypePrefixFromWeb = 'web-agreement-';
    // 人为上传的 other attachement, 任何类型文件
    final static String strTypePrefixOtherAttachmentFromWeb = 'web-other-attachment-';
    // before usage image
    final static String strUsageImageTypePrefixBefore = 'before-usage-';
    // after usage image
    final static String strUsageImageTypePrefixAfter = 'after-usage-';

    // 第一步骤用
    @AuraEnabled
    public static List<Object> getAllCustomer(String customerName){
        String keyWord = '%'+ customerName + '%';
        List<Object> lstRes = new List<Object>();

        // 需求明确，customer 必须是 status__c = Active 的。
        // 能够被筛选出来的 customer 类型
        List<String> lstAccRecordType = new List<String>();
        lstAccRecordType.add('Channel');
        lstAccRecordType.add('Association_Group');
        List<Account> lstAcc = new List<Account>();
        if (String.isNotBlank(customerName)) {
            lstAcc = [
                SELECT Id, Name,RecordType.DeveloperName
                ,CurrencyIsoCode
                FROM Account WHERE Id != NULL
                AND Name LIKE: keyWord
                AND RecordType.DeveloperName IN :lstAccRecordType
                AND Status__c = 'Active'
                ];
        } else {
            lstAcc = [
                SELECT Id, Name
                ,CurrencyIsoCode,RecordType.DeveloperName
                FROM Account WHERE Id != NULL
                AND RecordType.DeveloperName IN :lstAccRecordType
                AND Status__c = 'Active'
                ];
        }

        for (Account acc : lstAcc) {
            Map<String,Object> mapAcc = new Map<String,Object>();
            mapAcc.put('Currency', acc.CurrencyIsoCode);
            mapAcc.put('Id', acc.Id);
            mapAcc.put('Name', acc.Name);
            
            lstRes.add(mapAcc);
        }
        return lstRes;
    }

    @AuraEnabled
    public static List<Object> getProspectbyName(String prospectName){
        List<Object> lstRes = new List<Object>();

        String keyWord = '%'+ prospectName + '%';
        // 需求明确，如果是给 prospect， 币种取 系统 default
        CurrencyType objCurrenctType = [Select IsCorporate, IsoCode from CurrencyType  where IsCorporate = true LIMIT 1];
        // lead status 允许查询到的 状态
        List<String> lstProspectStatusAllowToFilter = new List<String>();
        lstProspectStatusAllowToFilter.add('Open');
        lstProspectStatusAllowToFilter.add('Assigned');
        lstProspectStatusAllowToFilter.add('Contacted');
        lstProspectStatusAllowToFilter.add('Qualified');
        lstProspectStatusAllowToFilter.add('Pending Review');
        lstProspectStatusAllowToFilter.add('Converted');

        List<Lead> lstProspect = new List<Lead>();
        if (String.isNotBlank(prospectName)) {
            lstProspect = [
                SELECT Id, Name
                FROM Lead WHERE Id != NULL
                AND Name LIKE: keyWord
                AND Status IN :lstProspectStatusAllowToFilter
            ];
        } else {
            lstProspect = [
                SELECT Id, Name
                FROM Lead WHERE Id != NULL
                AND Status IN :lstProspectStatusAllowToFilter
            ];
        }
        for (Lead prospect : lstProspect) {
            Map<String,Object> mapLead = new Map<String,Object>();
            mapLead.put('Currency', objCurrenctType.IsoCode);
            mapLead.put('Id', prospect.Id);
            mapLead.put('Name', prospect.Name);
            lstRes.add(mapLead);
        }

        return lstRes;
    }

    @AuraEnabled
    public static List<Account_Address__c> getAddressByLeadOrCustomer(String addressName, String recordType, String customerId, String prospectId){
        List<Account_Address__c> result = new List<Account_Address__c>();
        String str = 'SELECT Id, Name, Customer__c, Prospect__c, RecordType.Developername FROM Account_Address__c WHERE Id != NULL AND StatusNew__c =\'Active\'';
        //判断customer，Lead关联
        if (String.isNotBlank(customerId)) {
            str += ' AND Customer__c =: customerId';
        } else if (String.isNotBlank(prospectId)) {
            str += ' AND Prospect__c =: prospectId';
        }
        //判断recordtype类型
        if (recordType == 'Dropship Address') {
            str += ' AND RecordType.Developername = \'Dropship_Shipping_Address\'';
        } else {
            str += ' AND RecordType.Developername = \'Shipping_Address\'';
        }
        result = Database.query(str);
        return result;
    }

    // 找到子库中所有数据
    @AuraEnabled
    public static Map<String, Object> getAllSalesRepStock(){
        Map<String, Object> result = new Map<String, Object>();
        String loginUserId = userInfo.getUserId();
        Set<String> productSet = new Set<String>();
        // 管理员能看到所有
        List<Sales_Rep_Stock__c> salesRepstockList = new List<Sales_Rep_Stock__c>();

        // 只找子库 - Sales_Rep_Stock 的数据
        User usr = Util.getUserInfo(UserInfo.getUserId());
        if (usr.Profile.Name == Label.ADMIN) {
            salesRepstockList = [
                SELECT Id,Name
                ,RecordType.DeveloperName
                ,SerialNumber__c, Qty__c, CurrencyIsoCode,  Model__c
                ,Product_Description__c, Product_Description__r.Name, Product_Description__r.Order_Model__c
                ,Sales_Rep_Stock_Lentout__r.Customer__r.Name
                ,Sales_Rep_Stock_Lentout__r.Prospect__r.Name
                FROM Sales_Rep_Stock__c 
                WHERE Id != null 
                AND RecordType.DeveloperName = 'Sales_Rep_Stock'
                AND isDelete__c = false 
            ];
        } else {
            salesRepstockList = [
                SELECT Id,Name
                ,RecordType.DeveloperName
                ,SerialNumber__c, Qty__c, CurrencyIsoCode,  Model__c
                ,Product_Description__c, Product_Description__r.Name, Product_Description__r.Order_Model__c
                ,Sales_Rep_Stock_Lentout__r.Customer__r.Name
                ,Sales_Rep_Stock_Lentout__r.Prospect__r.Name
                FROM Sales_Rep_Stock__c 
                WHERE Id != null 
                AND RecordType.DeveloperName = 'Sales_Rep_Stock'
                AND isDelete__c = false 
                AND Request_For__c =: loginUserId 
            ];
        }
        // product ids， kits 展开 tools
        List<String> lstProductIds = new List<String>();
        // product 封装
        Map<String, List<salesRepStockWrapper>> productInfo = new Map<String, List<salesRepStockWrapper>>();
        for (Sales_Rep_Stock__c salesrepstock : salesRepstockList) {
            if(!productInfo.containsKey(salesrepstock.Product_Description__c)){
                productInfo.put(salesrepstock.Product_Description__c, new List<salesRepStockWrapper>());
            }
            salesRepStockWrapper salesrepstockWrapper = new salesRepStockWrapper();
            salesrepstockWrapper.snid = (String)salesrepstock.Id;
            salesrepstockWrapper.qty = (Integer)salesrepstock.Qty__c;
            salesrepstockWrapper.snno = salesrepstock.SerialNumber__c;
            salesrepstockWrapper.model = salesrepstock.Model__c;
            productInfo.get(salesrepstock.Product_Description__c).add(salesrepstockWrapper);
            productSet.add(salesrepstock.Product_Description__c);
        }
        List<Product2> productList = [
            SELECT Id, Name
            , Item_Description_EN__c, Order_Model__c
            , CurrencyIsoCode 
            FROM Product2 WHERE Id IN: productSet
        ];

        List<Object> lstData = new List<Object>();
        if (productList != null && productList.size() > 0) {
            for (Product2 product : productList) {
                Map<String,Object> mapItem = new Map<String,Object>();
                String idProduct = product.Id;
                mapItem.put('id', product.id);
                // product 的描述
                mapItem.put('name', product.Item_Description_EN__c);
                mapItem.put('totalQty', !productInfo.containsKey(product.id) ? 0 : productInfo.get(product.id).size());
                mapItem.put('model', product.Order_Model__c);
                mapItem.put('serialInfo', productInfo.get(product.id) == null ? new List<salesRepStockWrapper>() : productInfo.get(product.id));
                lstData.add(mapItem);        
            }
        }
        Map<String, String> currenctTypeMap = new map<String,String>();
        currenctTypeMap.put('currencyCode', UserInfo.getDefaultCurrency());
        result.put('state', 'SUCCESS');
        result.put('info', currenctTypeMap);
        result.put('data', lstData);
        return result;
    }

    //save or edit lent out
    @AuraEnabled
    public static Map<String, Object> saveLentout(String data, String status){
        System.debug('data:' + data);
        System.debug('status:' + status);
        Map<String, Object> result = new Map<String, Object>();
        Map<String, Object> lentOutData = (Map<String, Object>)JSON.deserializeUntyped(data);
        List<Object> snDataList = (List<Object>)lentOutData.get('serialInfo');
        List<Sales_RepStock_Lent_Out_Item__c> stLentoutItem = new List<Sales_RepStock_Lent_Out_Item__c>();
        System.debug('lentOutData--'+lentOutData);
         
        // 子库要修改的数据
        Savepoint objSavepoint;
        try {
            objSavepoint = Database.setSavepoint();
            // 保存或修改 salesrepstocklentout
            Sales_Rep_Stock_Lentout__c salesrepStock = new Sales_Rep_Stock_Lentout__c();
            // recordid 给值
            if (String.isNotBlank((String)lentOutData.get('recordId'))) {
                salesrepStock.Id = (String)lentOutData.get('recordId');
            }
            salesrepStock.CurrencyIsoCode = (String) lentOutData.get('currencyCode');
            
            if (String.isNotBlank((String)lentOutData.get('shippingAddressId'))) {
                salesrepStock.Shipping_Address__c = (String)lentOutData.get('shippingAddressId');
            }
            if (String.isNotBlank((String)lentOutData.get('drophipAddressId'))) {
                salesrepStock.Dropship_Address__c = (String)lentOutData.get('drophipAddressId');
            }
            
            salesrepStock.Usage_Start_Date__c = Date.valueOf((String)lentOutData.get('startDate'));
            salesrepStock.Usage_End_Date__c = Date.valueOf((String)lentOutData.get('endDate'));

            if ((Boolean)lentOutData.get('isProspect')) {
                salesrepStock.Prospect__c = (String)lentOutData.get('entityId');
            } else {
                salesrepStock.Customer__c = (String)lentOutData.get('entityId');
            }
            if (status == 'next') {
                salesrepStock.Status__c = 'Pending';
            } else if(status == 'draft'){
                salesrepStock.Status__c = 'Draft';
            }

            Database.upsert(salesrepStock, false);
            List<Sales_RepStock_Lent_Out_Item__c> stLentoutItem2Delete =[
                SELECT Id,Name
                FROM Sales_RepStock_Lent_Out_Item__c WHERE Id != NULL
                AND Sales_Rep_Stock_Lentout__c = :salesrepStock.Id
            ];
            if (stLentoutItem2Delete != null && stLentoutItem2Delete.size() > 0) {
                delete stLentoutItem2Delete;
            }
            

            //创建lentout item
            List<String> stockIds = new List<String>();
            //SalesRepStockId到sn的映射
            Map<String,String> mapStockId2sn = new Map<String,String>();
            System.debug('snDataList:' + JSON.serialize(snDataList));
            Set<String> setProductId = new Set<String>();

            for(Object snData : snDataList){
                Map<String, Object> sn = (Map<String, Object>) snData;
                setProductId.add(String.isBlank((String)sn.get('productId')) ? null : (String)sn.get('productId'));
            }
            //查询所有这个产品的借出信息
            List<Sales_RepStock_Lent_Out_Item__c> lstLentOutItemByProduct =[
                SELECT Id,Name,Usage_Start_Date__c,Usage_End_Date__c,product__c,product__r.Order_Model__c
                FROM Sales_RepStock_Lent_Out_Item__c WHERE Id != NULL
                AND product__c IN : setProductId
            ];
            Set<String> setErrorInfo = new Set<String>();
            //check Duplic Date
            for(Object snData : snDataList){
                Map<String, Object> sn = (Map<String, Object>) snData;
                String productId = String.isBlank((String)sn.get('productId')) ? null : (String)sn.get('productId');
                Date startDate = Date.valueOf((String)sn.get('startDate'));
                Date endDate = Date.valueOf((String)sn.get('endDate'));
                for(Sales_RepStock_Lent_Out_Item__c objLentout : lstLentOutItemByProduct){
                    if(productId == objLentout.product__c){
                        //start取最大值
                        Date maxDate = String.valueOf(objLentout.Usage_Start_Date__c)  > String.valueOf(startDate) ? 
                            Date.valueOf(objLentout.Usage_Start_Date__c) : Date.valueOf(startDate);
                        //endDate取最小值
                        Date minDate =  String.valueOf(objLentout.Usage_End_Date__c)  > String.valueOf(endDate) ? 
                        Date.valueOf(endDate) : Date.valueOf(objLentout.Usage_End_Date__c);
                        if(String.valueOf(maxDate) <= String.valueOf(minDate)){
                            //表示有交集
                            setErrorInfo.add(objLentout.product__r.Order_Model__c + ' '+ Label.Lent_Out_Duplic);
                        }

                    }

                }

            }
            if(setErrorInfo != null && setErrorInfo.size()>0){
                result.put('state', 'ERROR');
                result.put('errormsg', JSON.serialize(setErrorInfo).replace('[', '').replace(']', ''));
                return result;
            }

            

            for(Object snData : snDataList){
                Map<String, Object> sn = (Map<String, Object>) snData;
                Sales_RepStock_Lent_Out_Item__c items = new Sales_RepStock_Lent_Out_Item__c();
                items.product__c = String.isBlank((String)sn.get('productId')) ? null : (String)sn.get('productId');
                items.SerialNumber__c = String.isBlank((String)sn.get('snno')) ? null : (String)sn.get('snno');
                items.Sales_Rep_Stock__c = String.isBlank((String)sn.get('snid')) ? null : (String)sn.get('snid');
                items.Usage_Start_Date__c = Date.valueOf((String)sn.get('startDate'));
                items.Usage_End_Date__c = Date.valueOf((String)sn.get('endDate'));

                items.Sales_Rep_Stock_Lentout__c = salesrepStock.Id;
                stLentoutItem.add(items);
                stockIds.add((String)sn.get('snid'));
                //获取sn信息
                mapStockId2sn.put(items.Sales_Rep_Stock__c,(String)sn.get('snno'));
            }
            Database.upsert(stLentoutItem, false);
            // 生成pdf
            System.debug('salesrepStockId:' + salesrepStock.Id);
            Map<String,Object> pdfData = CCM_SalesRepStock_LentOut.getLentoutPdfInfoById(salesrepStock.Id);
            System.debug('pdfData:' + JSON.serialize(pdfData));
            // CCM_SalesRepStock_LentOut.generatePDF(salesrepStock.Id);
            // 修改子库数据
            // 子库的 lentout 保持最新
            if (status == 'next') {
                List<Sales_Rep_Stock__c> stockList = [SELECT Id, isLentout__c FROM Sales_Rep_Stock__c WHERE Id IN: stockIds];
                for (Sales_Rep_Stock__c stock : stockList) {
                    stock.isLentout__c = true;
                }
                update stockList;
            }
            CCM_SalesRepStock_Return.updateSnInfo(mapStockId2sn);

            result.put('recordId', salesrepStock.Id);
            result.put('state', 'SUCCESS');
        } catch (Exception e) {
            Database.rollback(objSavepoint);
            System.debug('error line number '+ e.getLineNumber() + 'error message: ' + e.getMessage());
            result.put('state', 'ERROR');
            result.put('errormsg', e.getMessage());
            result.put('stack', e.getStackTraceString());
        }
        return result;
    }

    // confirm 页面，获取
    @AuraEnabled
    public static Map<String,Object> getAgreementInfoOnConfirmPage(String recordId){
        Map<String,Object> mapRes = new Map<String,Object>();
        try {
            // lentout 信息
            Sales_Rep_Stock_Lentout__c currentLentout = [
                SELECT Id,Name
                ,Lent_Out_Number__c
                FROM Sales_Rep_Stock_Lentout__c WHERE Id != NULL
                AND Id = :recordId
                LIMIT 1
            ];
            Set<String> setRelatedDodumentId = new Set<String>();
            // 找到文件对象
            List<ContentDocumentLink> lstRelatedContentDocumentLink = [
                SELECT Id,ContentDocumentId 
                FROM ContentDocumentLink WHERE LinkedEntityId =:recordId 
            ];
            for (ContentDocumentLink cdl : lstRelatedContentDocumentLink) {
                setRelatedDodumentId.add(cdl.ContentDocumentId);
            }
            // 拿到 Title ，version url -》 可以下载的链接
            List<ContentVersion> lstRelatedContentVersions = [
                SELECT Id
                ,ContentDocumentId
                ,Title,PathOnClient,VersionDataUrl
                FROM ContentVersion WHERE Id != NULL
                AND ContentDocumentId IN :setRelatedDodumentId
            ];
            Map<String,Object> mapAgreement = new Map<String,Object>();
            ContentVersion currentAgreement = null;
            Boolean blAgreementExist = false;
            // 挂载前端要用的数据
            for (ContentVersion cv : lstRelatedContentVersions) {
                if (String.valueOf(cv.PathOnClient).contains(strTypePrefixFromSys)) {
                    mapAgreement.put('no', currentLentout.Lent_Out_Number__c);
                    mapAgreement.put('name', cv.Title);
                    mapAgreement.put('url', cv.VersionDataUrl);
                    mapAgreement.put('contentdocumentid', cv.ContentDocumentId);
                    blAgreementExist = true;
                    // 只会有一个 自动生成的pdf
                    break;
                }
            }
            if (blAgreementExist) {
                mapRes.put('state','SUCCESS');
                mapRes.put('data',mapAgreement);
            }

        } catch (Exception e) {
            mapRes.put('state','ERROR');
            mapRes.put('errormsg', e.getMessage());
        }
        return mapRes;
    }
    @AuraEnabled
    public static Map<String,Object> confirmLentoutRecord(String recordId) {
        Map<String,Object> mapRes = new Map<String,Object>();
        try {
            Sales_Rep_Stock_Lentout__c currentLentout = [
                SELECT Id,Name
                ,Status__c
                FROM Sales_Rep_Stock_Lentout__c WHERE Id != NULL
                AND Id = :recordId
                LIMIT 1
            ];
            currentLentout.Status__c = 'Confirmed';
            update currentLentout;

            mapRes.put('state', 'SUCCESS');
        } catch (Exception e) {
            mapRes.put('state', 'ERROR');
            mapRes.put('errormsg', e.getMessage());
            mapRes.put('stack', e.getStackTraceString());
        }

        return mapRes;
    }
    

    // 获取所有的媒体资源
    @AuraEnabled
    public static Map<String,Object> getMediaResourceOnViewPage(String recordId){
        Map<String,Object> mapRes = new Map<String,Object>();
        try {
            // lentout 信息
            Sales_Rep_Stock_Lentout__c currentLentout = [
                SELECT Id,Name
                ,Lent_Out_Number__c
                FROM Sales_Rep_Stock_Lentout__c WHERE Id != NULL
                AND Id = :recordId
                LIMIT 1
            ];
            Set<String> setRelatedDodumentId = new Set<String>();
            // 找到文件对象
            List<ContentDocumentLink> lstRelatedContentDocumentLink = [
                SELECT Id,ContentDocumentId 
                FROM ContentDocumentLink WHERE Id != NULL
                AND LinkedEntityId =:recordId 
            ];
            for (ContentDocumentLink cdl : lstRelatedContentDocumentLink) {
                setRelatedDodumentId.add(cdl.ContentDocumentId);
            }
            // 拿到 Title ，version url -》 可以下载的链接
            List<ContentVersion> lstRelatedContentVersions = [
                SELECT Id
                ,ContentDocumentId
                ,Title,PathOnClient,VersionDataUrl
                FROM ContentVersion WHERE Id != NULL
                AND ContentDocumentId IN :setRelatedDodumentId
            ];
            List<Object> lstSysAutoPdf = new List<Object>();
            List<Object> lstManualUploadPdf = new List<Object>();
            List<Object> lstBeforeUsageImage = new List<Object>();
            List<Object> lstAfterUsageImage = new List<Object>();
            List<Object> lstOtherAttachment = new List<Object>();
            // 挂载前端要用的数据
            for (ContentVersion cv : lstRelatedContentVersions) {
                Map<String,Object> mapMedia = new Map<String,Object>();

                // 系统生成的pdf
                if (String.valueOf(cv.PathOnClient).contains(strTypePrefixFromSys)) {
                    mapMedia.put('no', currentLentout.Lent_Out_Number__c);
                    mapMedia.put('name', cv.Title);
                    mapMedia.put('documentid', cv.ContentDocumentId);
                    mapMedia.put('url', cv.VersionDataUrl);
                    lstSysAutoPdf.add(mapMedia);
                    // 用户上传的pdf
                } else if (String.valueOf(cv.PathOnClient).contains(strTypePrefixFromWeb)) {
                    mapMedia.put('no', currentLentout.Lent_Out_Number__c);
                    mapMedia.put('name', cv.Title);
                    mapMedia.put('documentid', cv.ContentDocumentId);
                    mapMedia.put('url', cv.VersionDataUrl);
                    lstManualUploadPdf.add(mapMedia);

                    // 用户上传的 before usage image
                } else if (String.valueOf(cv.PathOnClient).contains(strUsageImageTypePrefixBefore)) {
                    mapMedia.put('no', currentLentout.Lent_Out_Number__c);
                    mapMedia.put('name', cv.Title);
                    mapMedia.put('documentid', cv.ContentDocumentId);
                    mapMedia.put('url', cv.VersionDataUrl);
                    lstBeforeUsageImage.add(mapMedia);

                    // 用户上传的 after usage image
                } else if (String.valueOf(cv.PathOnClient).contains(strUsageImageTypePrefixAfter)) {
                    mapMedia.put('no', currentLentout.Lent_Out_Number__c);
                    mapMedia.put('name', cv.Title);
                    mapMedia.put('documentid', cv.ContentDocumentId);
                    mapMedia.put('url', cv.VersionDataUrl);
                    lstAfterUsageImage.add(mapMedia);

                    // 用户上传的other Attachment
                } else if (String.valueOf(cv.PathOnClient).contains(strTypePrefixOtherAttachmentFromWeb)) {
                    mapMedia.put('no', currentLentout.Lent_Out_Number__c);
                    mapMedia.put('name', cv.Title);
                    mapMedia.put('documentid', cv.ContentDocumentId);
                    mapMedia.put('url', cv.VersionDataUrl);
                    lstOtherAttachment.add(mapMedia);

                }
            }
            mapRes.put('sysagreement', lstSysAutoPdf);
            mapRes.put('manualagreement', lstManualUploadPdf);
            mapRes.put('beforeusageimage', lstBeforeUsageImage);
            mapRes.put('afterusageimage', lstAfterUsageImage);
            mapRes.put('otherattachment', lstOtherAttachment);
        } catch (Exception e) {
 
        }
        return mapRes;
    }


    // honey Added 通过IdList 获取所有的媒体资源
    public static Map<String,Object> getMediaResourceByRecordList(List<String> lstRecordIds){
        Map<String,Object> mapLinkId2Attachment = new Map<String,Object>();
        
        try {
            // lentout 信息
            List<Sales_Rep_Stock_Lentout__c> lstCurrentLentout = [
                SELECT Id,Name
                ,Lent_Out_Number__c
                FROM Sales_Rep_Stock_Lentout__c WHERE Id != NULL
                AND Id = :lstRecordIds
            ];
            Map<String,String> mapLentoutId2Number = new Map<String,String>();
            for(Sales_Rep_Stock_Lentout__c objLentout : lstCurrentLentout){
                mapLentoutId2Number.put(objLentout.Id, objLentout.Lent_Out_Number__c);
            }
            Set<String> setRelatedDodumentId = new Set<String>();
            // 找到文件对象
            List<ContentDocumentLink> lstRelatedContentDocumentLink = [
                SELECT Id,ContentDocumentId ,LinkedEntityId
                FROM ContentDocumentLink WHERE Id != NULL
                AND LinkedEntityId IN :lstRecordIds 
            ];
            Map<String,String> mapDocumentId2LinkId = new Map<String,String>();
            for (ContentDocumentLink cdl : lstRelatedContentDocumentLink) {
                setRelatedDodumentId.add(cdl.ContentDocumentId);
                mapDocumentId2LinkId.put(cdl.ContentDocumentId, cdl.LinkedEntityId);
            }
            // 拿到 Title ，version url -》 可以下载的链接
            List<ContentVersion> lstRelatedContentVersions = [
                SELECT Id
                ,ContentDocumentId
                ,Title,PathOnClient,VersionDataUrl
                FROM ContentVersion WHERE Id != NULL
                AND ContentDocumentId IN :setRelatedDodumentId
            ];


            Map<String,List<ContentVersion>> mapLinkId2ContentVersion = new Map<String,List<ContentVersion>>();
            //将同一个对象的放在一起
            for (ContentVersion cv : lstRelatedContentVersions) {
                String linkId = mapDocumentId2LinkId.get(cv.ContentDocumentId);
                List<ContentVersion> lstContentByLink = mapLinkId2ContentVersion.containsKey(linkId) ?  mapLinkId2ContentVersion.get(linkId) : new List<ContentVersion>();
                lstContentByLink.add(cv);
                mapLinkId2ContentVersion.put(linkId,lstContentByLink);
            }
            //遍历同一个对象的数据
            for(String Key : mapLinkId2ContentVersion.keySet()){
                Map<String,Object> mapRes = new Map<String,Object>();
                List<Object> lstSysAutoPdf = new List<Object>();
                List<Object> lstManualUploadPdf = new List<Object>();
                List<Object> lstBeforeUsageImage = new List<Object>();
                List<Object> lstAfterUsageImage = new List<Object>();
                List<Object> lstOtherAttachment = new List<Object>();
                List<ContentVersion> lstContentByLink = mapLinkId2ContentVersion.get(key);
                for(ContentVersion cv : lstContentByLink){
                    Map<String,Object> mapMedia = new Map<String,Object>();
                    // 系统生成的pdf
                    if (String.valueOf(cv.PathOnClient).contains(strTypePrefixFromSys)) {
                        mapMedia.put('no',mapLentoutId2Number.get(Key) );
                        mapMedia.put('name', cv.Title);
                        mapMedia.put('documentid', cv.ContentDocumentId);
                        mapMedia.put('url', cv.VersionDataUrl);
                        lstSysAutoPdf.add(mapMedia);
                        // 用户上传的pdf
                    } else if (String.valueOf(cv.PathOnClient).contains(strTypePrefixFromWeb)) {
                        mapMedia.put('no', mapLentoutId2Number.get(Key) );
                        mapMedia.put('name', cv.Title);
                        mapMedia.put('documentid', cv.ContentDocumentId);
                        mapMedia.put('url', cv.VersionDataUrl);
                        lstManualUploadPdf.add(mapMedia);

                        // 用户上传的 before usage image
                    } else if (String.valueOf(cv.PathOnClient).contains(strUsageImageTypePrefixBefore)) {
                        mapMedia.put('no', mapLentoutId2Number.get(Key) );
                        mapMedia.put('name', cv.Title);
                        mapMedia.put('documentid', cv.ContentDocumentId);
                        mapMedia.put('url', cv.VersionDataUrl);
                        lstBeforeUsageImage.add(mapMedia);

                        // 用户上传的 after usage image
                    } else if (String.valueOf(cv.PathOnClient).contains(strUsageImageTypePrefixAfter)) {
                        mapMedia.put('no', mapLentoutId2Number.get(Key) );
                        mapMedia.put('name', cv.Title);
                        mapMedia.put('documentid', cv.ContentDocumentId);
                        mapMedia.put('url', cv.VersionDataUrl);
                        lstAfterUsageImage.add(mapMedia);

                        //用户上传的other Attachment
                    } else if(String.valueOf(cv.PathOnClient).contains(strTypePrefixOtherAttachmentFromWeb)) {
                        mapMedia.put('no',mapLentoutId2Number.get(Key) );
                        mapMedia.put('name', cv.Title);
                        mapMedia.put('documentid', cv.ContentDocumentId);
                        mapMedia.put('url', cv.VersionDataUrl);
                        lstOtherAttachment.add(mapMedia);

                    }
                }
                mapRes.put('sysagreement', lstSysAutoPdf);
                mapRes.put('manualagreement', lstManualUploadPdf);
                mapRes.put('beforeusageimage', lstBeforeUsageImage);
                mapRes.put('afterusageimage', lstAfterUsageImage);
                mapRes.put('otherattachment', lstOtherAttachment);
                mapLinkId2Attachment.put(Key, mapRes);
            }
        } catch (Exception e) {
 
        }
        return mapLinkId2Attachment;
    }

    // 通过id展示所有--->同步前的数据
    @AuraEnabled
    public static Map<String, Object> getLentoutInfoById(String lentoutId){
        Map<String, Object> result = new Map<String, Object>();
        try {
            List<Sales_Rep_Stock_Lentout__c> lstSalesRepStock = [
            SELECT Id, Name
                ,Lent_Out_Number__c,Status__c
                ,Customer__c,Customer__r.Name,Prospect__c,Prospect__r.Name
                ,Shipping_Address__c,Shipping_Address__r.Final_Address__c,Dropship_Address__c,Dropship_Address__r.Final_Address__c
                ,Usage_Start_Date__c,Usage_End_Date__c
                ,CreatedDate,CreatedBy.Name,CreatedById
                    ,(SELECT Id
                        ,Product__c, Product__r.Name, Product__r.Order_Model__c,Product__r.Item_Description_EN__c
                        ,SerialNumber__c,Sales_Rep_Stock__c,Usage_Start_Date__c,Usage_End_Date__c 
                        FROM  Sales_RepStock_Lent_Out_Items__r)
            FROM Sales_Rep_Stock_Lentout__c 
            WHERE Id != NULL
            AND Id =: lentoutId
            ];
            if (lstSalesRepStock == null || lstSalesRepStock.size() == 0) {
                result.put('state', 'ERROR');
                result.put('errormsg', 'No data!');
                return result;
            }
            Sales_Rep_Stock_Lentout__c salesRepStock = lstSalesRepStock.get(0);
            System.debug('test====='+salesRepStock);

            Map<String,Object> mapAgreement = new Map<String,Object>();
            String strNameVFAgreement = lstSalesRepStock.get(0).Lent_Out_Number__c;

            // lentoutInfo
            Map<String, Object> lentoutMapItem = new Map<String, Object>();
            lentoutMapItem.put('Id', salesRepStock.Id);
            lentoutMapItem.put('Status', salesRepStock.Status__c);
            lentoutMapItem.put('LentOutNumber', salesRepStock.Lent_Out_Number__c);
            lentoutMapItem.put('LentOutName',salesRepStock.Name);
            
            lentoutMapItem.put('Customer',new Map<String, Object>{'label' => salesRepStock.Customer__r.Name == null ? '' : salesRepStock.Customer__r.Name, 'value' => salesRepStock.Customer__c == null ? '' : salesRepStock.Customer__c});
            lentoutMapItem.put('Prospect', new Map<String, Object>{'label' => salesRepStock.Prospect__r.Name == null ? '': salesRepStock.Prospect__r.Name, 'value' => salesRepStock.Prospect__c == null ? '' : salesRepStock.Prospect__c});
            
            lentoutMapItem.put('ShippingAddress', new Map<String, Object>{'label' => salesRepStock.Shipping_Address__r.Final_Address__c == null ? '': salesRepStock.Shipping_Address__r.Final_Address__c, 'value' => salesRepStock.Shipping_Address__c == null ? '': salesRepStock.Shipping_Address__c});
            lentoutMapItem.put('DropshipAddress', new Map<String, Object>{'label' => salesRepStock.Dropship_Address__r.Final_Address__c == null ? '': salesRepStock.Dropship_Address__r.Final_Address__c, 'value' => salesRepStock.Dropship_Address__c == null ? '': salesRepStock.Dropship_Address__c});
            
            lentoutMapItem.put('UsageStartDate', salesRepStock.Usage_Start_Date__c);
            lentoutMapItem.put('UsageEndDate', salesRepStock.Usage_End_Date__c);
            lentoutMapItem.put('CreateDate', salesRepStock.CreatedDate);
            lentoutMapItem.put('CreatureUser', new Map<String, Object>{'label' => salesRepStock.CreatedBy.Name, 'value' => salesRepStock.CreatedById});

            // Product Info
            List<Object> productInfoList = new List<Object>();
            for (Sales_RepStock_Lent_Out_Item__c snproduct : salesRepStock.Sales_RepStock_Lent_Out_Items__r) {
                String idProduct = snproduct.Product__c;
                Map<String, Object> productInfoMapItem = new Map<String, Object>();
                productInfoMapItem.put('SalesRepStock',snproduct.Sales_Rep_Stock__c);

                productInfoMapItem.put('SerialNumber',snproduct.SerialNumber__c);
                productInfoMapItem.put('Product',new Map<String, Object>{'label' => snproduct.Product__r.Item_Description_EN__c == null ? '': snproduct.Product__r.Item_Description_EN__c, 'value' => snproduct.Product__c});
                productInfoMapItem.put('Model', snproduct.Product__r.Order_Model__c);
                // 需求明确，qty 永远只能为 1
                productInfoMapItem.put('Qty', 1);
            
                productInfoMapItem.put('Customer',new Map<String, Object>{'label' => salesRepStock.Customer__r.Name == null ? '' : salesRepStock.Customer__r.Name, 'value' => salesRepStock.Customer__c == null ? '' : salesRepStock.Customer__c});
                productInfoMapItem.put('Prospect', new Map<String, Object>{'label' => salesRepStock.Prospect__r.Name == null ? '': salesRepStock.Prospect__r.Name, 'value' => salesRepStock.Prospect__c == null ? '' : salesRepStock.Prospect__c});

                productInfoMapItem.put('UsageStartDate', snproduct.Usage_Start_Date__c);
                productInfoMapItem.put('UsageEndDate', snproduct.Usage_End_Date__c);
                productInfoList.add(productInfoMapItem);
            }
            // 媒体资源
            Map<String,Object> mapMedia = getMediaResourceOnViewPage(lentoutId);

            result.put('productInfo', productInfoList);
            result.put('lentOutInfo', lentoutMapItem);
            result.put('mediasource', mapMedia);

            result.put('state', 'SUCCESS');
        } catch (Exception ex) {
            result.put('state', 'ERROR');
            result.put('errormsg', ex.getMessage());
            result.put('debug', ex.getStackTraceString());
        }
        System.debug('result:' + JSON.serialize(result));
        return result;
    }
    // 通过id展示所有--->同步前的数据
    @AuraEnabled
    public static Map<String, Object> getLentoutPdfInfoById(String lentoutId){
        Map<String, Object> result = new Map<String, Object>();
        try {
            List<Sales_Rep_Stock_Lentout__c> lstSalesRepStock = [
            SELECT Id, Name
                ,Lent_Out_Number__c
                ,Customer__c,Customer__r.Name,Prospect__c,Prospect__r.Name,Shipping_Address__r.Final_Address__c
                ,Shipping_Address__c,Shipping_Address__r.Name,Dropship_Address__c,Dropship_Address__r.Name,Dropship_Address__r.Final_Address__c
                ,Usage_Start_Date__c,Usage_End_Date__c
                ,CreatedDate,CreatedBy.Name,CreatedById
                    ,(SELECT Id
                        ,Product__c, Product__r.Name, Product__r.Order_Model__c
                        ,SerialNumber__c,Sales_Rep_Stock__c,Usage_Start_Date__c,Usage_End_Date__c 
                        FROM  Sales_RepStock_Lent_Out_Items__r)
            FROM Sales_Rep_Stock_Lentout__c 
            WHERE Id != NULL
            AND Id =: lentoutId
            ];
            System.debug('lstSalesRepStock:' + JSON.serialize(lstSalesRepStock));
            if (lstSalesRepStock == null || lstSalesRepStock.size() == 0) {
                result.put('state', 'ERROR');
                result.put('errormsg', 'No data!');
                return result;
            }
            Sales_Rep_Stock_Lentout__c salesRepStock = lstSalesRepStock.get(0);

            // lentoutInfo
            Map<String, Object> lentoutMapItem = new Map<String, Object>();
            lentoutMapItem.put('LentOutNumber', salesRepStock.Lent_Out_Number__c);
            lentoutMapItem.put('LentOutName',salesRepStock.Name);
            lentoutMapItem.put('Customer',new Map<String, Object>{'label' => salesRepStock.Customer__r.Name == null ? '/' : salesRepStock.Customer__r.Name, 'value' => salesRepStock.Customer__c == null ? null : salesRepStock.Customer__c});
            lentoutMapItem.put('Prospect', new Map<String, Object>{'label' => salesRepStock.Prospect__r.Name == null ? '/': salesRepStock.Prospect__r.Name, 'value' => salesRepStock.Prospect__c == null ? null : salesRepStock.Prospect__c});
            lentoutMapItem.put('ShippingAddress', new Map<String, Object>{'label' => salesRepStock.Shipping_Address__r.Name == null ? '/': salesRepStock.Shipping_Address__r.Name, 'value' => salesRepStock.Shipping_Address__c == null ? null: salesRepStock.Shipping_Address__c});
            lentoutMapItem.put('DropshipAddress', new Map<String, Object>{'label' => salesRepStock.Dropship_Address__r.Name == null ? '/': salesRepStock.Dropship_Address__r.Name, 'value' => salesRepStock.Dropship_Address__c == null ? null: salesRepStock.Dropship_Address__c});
            lentoutMapItem.put('UsageStartDate', salesRepStock.Usage_Start_Date__c.format());
            lentoutMapItem.put('UsageEndDate', salesRepStock.Usage_End_Date__c.format());
            lentoutMapItem.put('CreateDate', salesRepStock.CreatedDate);
            lentoutMapItem.put('CreatureUser', new Map<String, Object>{'label' => salesRepStock.CreatedBy.Name, 'value' => salesRepStock.CreatedById});

            // Product Info
            List<Map<String, Object>> productInfoList = new List<Map<String, Object>>();
            System.debug('Sales_RepStock_Lent_Out_Items__r:' + JSON.serialize(salesRepStock.Sales_RepStock_Lent_Out_Items__r));
            Integer intIndex = 1;
            for (Sales_RepStock_Lent_Out_Item__c snproduct : salesRepStock.Sales_RepStock_Lent_Out_Items__r) {
                Map<String, Object> productInfoMapItem = new Map<String, Object>();
                productInfoMapItem.put('SerialNumber', String.isBlank(snproduct.SerialNumber__c) ? '' : snproduct.SerialNumber__c);
                productInfoMapItem.put('Index', String.valueOf(intIndex));
                productInfoMapItem.put('Product',new Map<String, Object>{'label' => snproduct.Product__r.Name == null ? '/': snproduct.Product__r.Name, 'value' => snproduct.Product__c});
                productInfoMapItem.put('Model', String.isBlank(snproduct.Product__r.Order_Model__c) ? '' : snproduct.Product__r.Order_Model__c);
                productInfoMapItem.put('UsageStartDate', snproduct.Usage_Start_Date__c.format());
                productInfoMapItem.put('UsageEndDate',snproduct.Usage_End_Date__c.format());
                productInfoList.add(productInfoMapItem);
                intIndex ++ ;
            }
            
            result.put('productInfo', productInfoList);
            result.put('lentOutInfo', lentoutMapItem);
            result.put('state', 'SUCCESS');
            System.debug('result:' + JSON.serialize(result));
        } catch (Exception ex) {
            result.put('state', 'ERROR');
            result.put('errormsg', ex.getMessage());
            result.put('debug', ex.getStackTraceString());
        }
        System.debug('result:' + JSON.serialize(result));
        return result;
    }
    // 根据 invoice visual force page 生成 PDF 文件
    @AuraEnabled
    public static String generatePDF(String recordId){
        String entityName = '';
        Sales_Rep_Stock_Lentout__c currentLentOut = [
            SELECT Id,Name
            ,Lent_Out_Number__c
            ,Customer__c,Customer__r.Name
            ,Prospect__c,Prospect__r.Name
            FROM Sales_Rep_Stock_Lentout__c WHERE Id != NULL
            AND Id = :recordId
            LIMIT 1
        ];
        String strNameOfAgreement = '';
        // 德文翻译
        String strAgreementPrefix = Label.CCM_Agreement_With + ' ';
        
        Date today = Date.today();
        String strDate = today.year()+  String.valueOf(today.Month()).leftPad(2, '0') + String.valueOf(today.day()).leftPad(2, '0');
        
        // 有且只能有 customer or prospect
        // 添加文件名 - customer or prospect 的 name
        if (String.isNotBlank(currentLentOut.Customer__c)) {
            entityName = currentLentOut.Customer__r.Name;
        } else if (String.isNotBlank(currentLentOut.Prospect__c)) {
            entityName = currentLentOut.Prospect__r.Name;
        }

        String strNamePrefixOfAgreement =  strAgreementPrefix + entityName + '-No.' + currentLentOut.Lent_Out_Number__c;
        String strNoAgreement = currentLentOut.Lent_Out_Number__c;
        // Agreement with {customer / prospect 's name'} – No.00007 - 20230503.pdf
        strNameOfAgreement = strNamePrefixOfAgreement + '-' + strDate + '.pdf';

        PageReference pageRef = Page.salesrepstockLentoutPDFView;
        pageRef.getParameters().put('recordId', recordId);                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
       
        // 需要先删除之前的自动生成的pdf
        Set<String> setPdfBefore = new Set<String>();

        Set<String> setRelatedDodument = new Set<String>();
        Set<String> setRelatedDodumentId = new Set<String>();
        
        // 要删除的 document
        Map<String,ContentDocument> mapDocId2Doc = new Map<String,ContentDocument>();
        List<ContentDocument> lstBeforePdf2Del = new List<ContentDocument>();

        // 要删除的之前的文件的id
        Set<String> setRelatedDodumentId2Del = new Set<String>();
        List<ContentDocumentLink> lstRelatedContentDocumentLink = [
            SELECT Id,ContentDocumentId 
            FROM ContentDocumentLink WHERE LinkedEntityId =:recordId 
        ];
        for (ContentDocumentLink cdl : lstRelatedContentDocumentLink) {
            setRelatedDodumentId.add(cdl.ContentDocumentId);
        }

        List<ContentDocument> lstRelatedContentDocs = [
            SELECT Id
            FROM ContentDocument WHERE Id != NULL
            AND Id IN :setRelatedDodumentId
        ];
        for (ContentDocument cd : lstRelatedContentDocs) {
            mapDocId2Doc.put(cd.Id,cd);
        }
        List<ContentVersion> lstRelatedContentVersions = [
            SELECT Id
            ,ContentDocumentId
            ,Title,PathOnClient
            FROM ContentVersion WHERE Id != NULL
            AND ContentDocumentId IN :setRelatedDodumentId
        ];
        // 检查pdf文件信息中，是否 sys，是否是以前的 pdf 文件
        for (ContentVersion cv : lstRelatedContentVersions) {
            if (String.isNotBlank(cv.PathOnClient)
                && String.valueOf(cv.PathOnClient).contains(strTypePrefixFromSys)
            ) {
                ContentDocument docTmp = mapDocId2Doc.get(cv.ContentDocumentId);
                if (docTmp != null) {
                    lstBeforePdf2Del.add(docTmp);
                }
            }
        }
        System.debug('lstBeforePdf2Del:' + lstBeforePdf2Del);
        if (lstBeforePdf2Del.size() > 0) {
            delete lstBeforePdf2Del;
        }

        ContentVersion conVer = new ContentVersion();
        conVer.ContentLocation = 'S';
        // sys-agreement 意味着是系统自动生成的pdf
        conVer.PathOnClient = strTypePrefixFromSys + strNameOfAgreement;
        conVer.Title = strNameOfAgreement;
        conVer.VersionData = (Blob)pageRef.getContent();
        insert conVer;

        ContentDocumentLink cdl = new ContentDocumentLink();
        cdl.ContentDocumentId = [
            SELECT Id, ContentDocumentId 
            FROM ContentVersion WHERE Id != NULL
            AND Id =: conVer.Id
        ].ContentDocumentId;

        cdl.LinkedEntityId = recordId ;
        cdl.ShareType = 'V';
        cdl.Visibility = 'AllUsers';
        insert cdl;

        return conVer.id;
    }   

    // aggrement,上传
    @AuraEnabled
    public static Map<String, Object> uploadLentoutAgreementPDF(String recordId, String fileData){
        Map<String, Object> result = new Map<String, Object>();
        try {
            System.debug('fileData:' + fileData);
            Map<String,Object> mapFileData = (Map<String,Object>)JSON.deserializeUntyped(fileData);

            String fileName = (String)mapFileData.get('name');
            String fileType = (String)mapFileData.get('type');
            String fileContent = (String)mapFileData.get('content');

            Sales_Rep_Stock_Lentout__c salesRepStock = [
                SELECT Id,Status__c
                FROM Sales_Rep_Stock_Lentout__c WHERE Id != NULL
                AND Id =: recordId
            ];

            ContentVersion conVer = new ContentVersion();
            conVer.ContentLocation = 'S';
            // strTypePrefixFromWeb = web-agreement 意味着是人为上传的pdf
            conVer.PathOnClient = strTypePrefixFromWeb + fileName;
            conVer.Title = fileName;
            conVer.VersionData = (Blob)EncodingUtil.base64Decode(fileContent);
            insert conVer;

            ContentDocumentLink cdl = new ContentDocumentLink();
            cdl.ContentDocumentId = [
                SELECT Id, ContentDocumentId 
                FROM ContentVersion WHERE Id != NULL
                AND Id =: conVer.Id
            ].ContentDocumentId;

            cdl.LinkedEntityId = recordId ;
            cdl.ShareType = 'V';
            cdl.Visibility = 'AllUsers';
            insert cdl;

            salesRepStock.Status__c = 'End';
            update salesRepStock;

            result.put('state', 'SUCCESS');
        } catch (Exception e) {
            System.debug('error line number: '+ e.getLineNumber());
            System.debug('error message: '+ e.getMessage());
            result.put('state', 'ERROR');
            result.put('errormsg', e.getMessage());
        }
        return result;
    }

    // other attachment 页面上传任意类型的 文件
    @AuraEnabled
    public static Map<String, Object> uploadLentoutOtherAttachment(String recordId, String fileData){
        Map<String, Object> result = new Map<String, Object>();
        try {
            System.debug('fileData:' + fileData);
            Map<String,Object> mapFileData = (Map<String,Object>)JSON.deserializeUntyped(fileData);

            String fileName = (String)mapFileData.get('name');
            String fileType = (String)mapFileData.get('type');
            String fileContent = (String)mapFileData.get('content');

            Sales_Rep_Stock_Lentout__c salesRepStock = [
                SELECT Id,Status__c
                FROM Sales_Rep_Stock_Lentout__c WHERE Id != NULL
                AND Id =: recordId
            ];

            ContentVersion conVer = new ContentVersion();
            conVer.ContentLocation = 'S';
            // strTypePrefixOtherAttachmentFromWeb = 'web-other-attachment-' 意味着是人为上传的 任意文件
            conVer.PathOnClient = strTypePrefixOtherAttachmentFromWeb + fileName;
            conVer.Title = fileName;
            conVer.VersionData = (Blob)EncodingUtil.base64Decode(fileContent);
            insert conVer;

            ContentDocumentLink cdl = new ContentDocumentLink();
            cdl.ContentDocumentId = [
                SELECT Id, ContentDocumentId 
                FROM ContentVersion WHERE Id != NULL
                AND Id =: conVer.Id
            ].ContentDocumentId;

            cdl.LinkedEntityId = recordId ;
            cdl.ShareType = 'V';
            cdl.Visibility = 'AllUsers';
            insert cdl;

            salesRepStock.Status__c = 'End';
            update salesRepStock;

            result.put('state', 'SUCCESS');
        } catch (Exception e) {
            System.debug('error line number: '+ e.getLineNumber());
            System.debug('error message: '+ e.getMessage());
            result.put('state', 'ERROR');
            result.put('errormsg', e.getMessage());
        }
        return result;
    }

    // aggrement,使用前图片和使用后图片的 保存
    @AuraEnabled
    public static Map<String, String> uploadLentoutUsageImage(String recordId, String fileData){
        Map<String, String> result = new Map<String, String>();
        try {
            System.debug('fileData:' + fileData);
            Map<String,Object> mapFileData = (Map<String,Object>)JSON.deserializeUntyped(fileData);

            String fileName = (String)mapFileData.get('name');
            String fileType = (String)mapFileData.get('type');
            String fileBeforeOrAfter = (String)mapFileData.get('usage');
            String fileContent = (String)mapFileData.get('content');
            String filePathOnClient = '';
            if (fileBeforeOrAfter == 'before') {
                filePathOnClient = strUsageImageTypePrefixBefore + fileName;
            } else if (fileBeforeOrAfter == 'after') {
                filePathOnClient = strUsageImageTypePrefixAfter + fileName;
            }


            ContentVersion conVer = new ContentVersion();
            conVer.ContentLocation = 'S';
            conVer.PathOnClient = filePathOnClient;
            conVer.Title = fileName;
            conVer.VersionData = (Blob)EncodingUtil.base64Decode(fileContent);
            insert conVer;

            ContentDocumentLink cdl = new ContentDocumentLink();
            cdl.ContentDocumentId = [
                SELECT Id, ContentDocumentId 
                FROM ContentVersion WHERE Id != NULL
                AND Id =: conVer.Id
            ].ContentDocumentId;

            cdl.LinkedEntityId = recordId ;
            cdl.ShareType = 'V';
            cdl.Visibility = 'AllUsers';
            insert cdl;

            result.put('state', 'SUCCESS');
            return result;
        } catch (Exception e) {
            System.debug('error line number: '+ e.getLineNumber());
            System.debug('error message: '+ e.getMessage());
            result.put('state', 'ERROR');
            result.put('state', 'ERROR');
            return result;
        }
    }

   //lent out history
   @AuraEnabled
   public static List<Map<String, Object>> getLentOutHistory(){
        String sqlOrderString = ' SELECT Id, Lent_Out_Number__c, Customer__c, Customer__r.Name, Prospect__c, Prospect__r.Name, Usage_Start_Date__c, Usage_End_Date__c '+
        ' FROM  Sales_Rep_Stock_Lentout__c  WHERE Id != null ';
        User usr = Util.getUserInfo(UserInfo.getUserId());
        String strUserId = usr.Id;
        if(usr.Profile.Name != Label.ADMIN){
            sqlOrderString += ' AND  CreatedById = :strUserId  ';
        }
        sqlOrderString += ' Order By CreatedDate desc limit 49999';
        List<Sales_Rep_Stock_Lentout__c> lentOutList = Database.query(sqlOrderString);
        List<Map<String, Object>> resultList = new List<Map<String, Object>>();
        for (Sales_Rep_Stock_Lentout__c lentout : lentOutList) {
            Map<String, Object> lentItem = new Map<String, Object>();
            lentItem.put('Id', lentout.Id);
            lentItem.put('lentoutnumber', lentout.Lent_Out_Number__c);
            lentItem.put('customer', lentout.Customer__r.Name == null ? '/' : lentout.Customer__r.Name);
            lentItem.put('prospect', lentout.Prospect__r.Name == null ? '/' : lentout.Prospect__r.Name);
            lentItem.put('startdate', lentout.Usage_Start_Date__c);
            lentItem.put('enddate', lentout.Usage_End_Date__c);
            resultList.add(lentItem);
        }
        return resultList;
   } 

   @AuraEnabled
    public static String QueryAddressByName(String AddressName, String customerId, String recordTypeName){
        return CCM_FillPurchaseInfoController.QueryAddressByName(AddressName,customerId,recordTypeName);
    }

    public class salesRepStockWrapper{
        @AuraEnabled public String snid{get;set;}
        @AuraEnabled public Integer qty{get;set;}
        @AuraEnabled public String snno{get; set;}
        @AuraEnabled public String productid{get; set;}
        @AuraEnabled public String lentcustomername{get; set;}
        @AuraEnabled public String lentprospectname{get; set;}
        @AuraEnabled public String model{get; set;}
        // 实际保存的是 product 的 description
        @AuraEnabled public String name{get; set;}
        @AuraEnabled public Decimal price{get; set;}
        // usagetype
        @AuraEnabled public String usagetype{get; set;}
        // product rating status
        @AuraEnabled public String ratingstatus{get; set;}

        public salesRepStockWrapper(){
            this.snid = '';
            this.qty = 0;
            this.snno = '';
            this.productid = '';
            this.lentcustomername = '';
            this.lentprospectname = '';
            this.model = '';
            this.name = '';
            this.price = 0.00;
            this.usagetype = '';
            this.ratingstatus = '';
        }
    }
}