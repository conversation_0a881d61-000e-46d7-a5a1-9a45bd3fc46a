import xml.etree.ElementTree as ET
import os
import shutil

def process_files(dir_path, whole_folder, metadata_type):
    """
    compare profile changed with whole profile, update whole profile
    """
    if not os.path.isdir(dir_path):
       return
    for file_name in os.listdir(dir_path):
        metadata_changed = get_files_changed(dir_path, file_name, metadata_type)
        if metadata_type == 'profiles':
            original_file_path = whole_folder + '/' + file_name
        elif metadata_type == 'labels':
            original_file_path = whole_folder + '/' + 'CustomLabels.labels-meta.xml'
        elif metadata_type == 'translations':
            original_file_path = whole_folder + '/' + 'de.translation-meta.xml'
        print(original_file_path)
        metadata_file_whole = get_file_whole(file_name, original_file_path, dir_path, metadata_type)
        if metadata_file_whole is not None:
            # iterate profile change to compare with whole profile
            for key, item in metadata_changed.items():
                # replace or insert changed segment
                metadata_file_whole[key] = item
        # sort profile elements
        if metadata_file_whole is not None:
            whole_eles = sort_eles(metadata_file_whole)
            update_file(file_name, whole_eles, whole_folder, metadata_type)


def get_files_changed(dir_path, file_name, metadata_type):
    file_path = dir_path + '/' + file_name
    # print(file_path)
    metadata = construct_xml_tree(file_path, metadata_type)
    return metadata


def get_file_whole(file_name, origin_file_path, package_path, metadata_type):
    metadata = None
    # dir_path = r'./profiles'
    # file_path = os.path.join(origin_file_path, file_name)
    if os.path.exists(origin_file_path):
        metadata = construct_xml_tree(origin_file_path, metadata_type)
    else:
        shutil.copy(package_path + '/' + file_name, origin_file_path)
    return metadata


def update_file(file_name, whole_eles, whole_path, metadata_type):
    file_path = whole_path + '/' + file_name
    if metadata_type == 'labels':
        file_path = whole_path + '/' + 'CustomLabels.labels-meta.xml'
    if metadata_type == 'translations':
        file_path = whole_path + '/' + 'de.translation-meta.xml'
    whole_tree = ET.parse(file_path)
    root = whole_tree.getroot()
    root.clear()
    root.extend(whole_eles)
    ET.register_namespace('', 'http://soap.sforce.com/2006/04/metadata')
    whole_tree.write(file_path, encoding='UTF-8', default_namespace=None, xml_declaration=True)


def sort_eles(metadata):
    keys = list(metadata.keys())
    keys.sort()
    sorted_eles = [metadata[key] for key in keys]
    # print(keys)
    return sorted_eles


def construct_xml_tree(file_path, metadata_type):
    metadata = {}
    tree = ET.parse(file_path)
    for child in tree.getroot():
        key = ConstructKey(child, metadata_type)
        if key is not None:
            metadata[key] = child
    return metadata


def ConstructKey(ele, metadata_type):
    key = None
    if metadata_type == 'profiles':
        key = ConstructKey_profile(ele)
    if metadata_type == 'labels':
        key = ConstructKey_label(ele)
    if metadata_type == 'translations':
        key = ConstructKey_translation(ele)
    return key


def ConstructKey_profile(ele):
    key = None
    if 'customMetadataTypeAccesses' in ele.tag:
        for innerChild in ele:
            if 'name' in innerChild.tag:
                key = ele.tag + innerChild.text
    elif 'fieldPermissions' in ele.tag:
        for innerChild in ele:
            if 'field' in innerChild.tag:
                key = ele.tag + innerChild.text
    elif 'loginIpRanges' in ele.tag:
        key = ele.tag
    elif 'objectPermissions' in ele.tag:
        for innerChild in ele:
            if 'object' in innerChild.tag:
                key = ele.tag + innerChild.text
    elif 'recordTypeVisibilities' in ele.tag:
        for innerChild in ele:
            if 'recordType' in innerChild.tag:
                key = ele.tag + innerChild.text
    elif 'userPermissions' in ele.tag:
        for innerChild in ele:
            if 'name' in innerChild.tag:
                key = ele.tag + innerChild.text
    elif 'classAccesses' in ele.tag:
        for innerChild in ele:
            if 'apexClass' in innerChild.tag:
                key = ele.tag + innerChild.text
    elif 'flowAccesses' in ele.tag:
        for innerChild in ele:
            if 'flow' in innerChild.tag:
                key = ele.tag + innerChild.text
    elif 'layoutAssignments' in ele.tag:
        key = ele.tag
        for innerChild in ele:
            if 'layout' in innerChild.tag or 'recordType' in innerChild.tag:
                key = key + innerChild.text
    elif 'pageAccesses' in ele.tag:
        for innerChild in ele:
            if 'apexPage' in innerChild.tag:
                key = ele.tag + innerChild.text
    elif 'tabVisibilities' in ele.tag:
        for innerChild in ele:
            if 'tab' in innerChild.tag:
                key = ele.tag + innerChild.text
    elif 'applicationVisibilities' in ele.tag:
        for innerChild in ele:
            if 'application' in innerChild.tag:
                key = ele.tag + innerChild.text
    else:
        key = ele.tag + ele.text
    return key


def ConstructKey_label(ele):
    key = None
    if 'labels' in ele.tag:
        for innerChild in ele:
            if 'fullName' in innerChild.tag:
                key = ele.tag + innerChild.text
    else:
        key = ele.tag + ele.text
    return key

def ConstructKey_translation(ele):
    key = None
    if 'customLabels' in ele.tag:
        for innerChild in ele:
            if 'name' in innerChild.tag:
                key = ele.tag + innerChild.text
    else:
        key = ele.tag + ele.text
    return key

dir_path = 'force-app/main/default/profiles'
whole_profile_folder = 'profiles'
process_files(dir_path, whole_profile_folder, 'profiles')

dir_path = 'force-app/main/default/labels'
whole_profile_folder = 'labels'
process_files(dir_path, whole_profile_folder, 'labels')

dir_path = 'force-app/main/default/translations'
whole_profile_folder = 'translations'
process_files(dir_path, whole_profile_folder, 'translations')