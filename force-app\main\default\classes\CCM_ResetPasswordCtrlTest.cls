/**
 * Test class for CCM_ResetPasswordCtrl
 */
@isTest
private class CCM_ResetPasswordCtrlTest {

    @TestSetup
    static void makeData(){
        Account testAccount = new Account(Name = 'Test Account', Standard_Edit_Process__c = true, RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Channel').getRecordTypeId(), Approval_Status__c = 'Approved');
        insert testAccount;
    }

    @isTest
    static void resetPasswordTest() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CCM_ResetPasswordCtrl.resetPassword(acc.Id);
    }
}