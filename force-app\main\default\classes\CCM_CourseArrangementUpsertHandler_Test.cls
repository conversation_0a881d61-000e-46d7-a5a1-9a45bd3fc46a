/**
 * Honey
 * 2023/10/30
 * 测试类
 */
@isTest
public without sharing class CCM_CourseArrangementUpsertHandler_Test {
    @isTest
    public static void testUpdatePriceBook(){
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Traning_PriceBook__c objTraining = new Traning_PriceBook__c();
        objTraining.Start_Date__c = Date.today().addDays(-100);
        objTraining.End_Date__c = Date.today().addDays(100);
        objTraining.IsActive__c = true;
        insert objTraining;
        Traning_PriceBook_Entry__c objTrainingBook = new Traning_PriceBook_Entry__c();
        objTrainingBook.Course_Product__c = objProduct.Id;
        objTrainingBook.Traning_PriceBook__c = objTraining.Id;
        objTrainingBook.Start_Date__c = Date.today().addDays(-100);
        objTrainingBook.End_Date__c = Date.today().addDays(100);
        objTrainingBook.UnitPrice__c = 20;
        insert objTrainingBook;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        Course_Arrangement__c objCourseArrangement = new Course_Arrangement__c();
        objCourseArrangement.Course_Date__c = Date.today();

        objCourseArrangement.Training_Course_Setting__c = objSetting.Id;
        insert objCourseArrangement;
        objCourseArrangement.Traning_PriceBook__c = objTraining.Id;
        update objCourseArrangement;

    }
    
    public CCM_CourseArrangementUpsertHandler_Test() {

    }
}