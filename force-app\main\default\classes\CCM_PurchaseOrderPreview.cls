/**
* Author: Honey
* Description: 用户填完Order信息以及选择产品后对前面所有信息做一个预览。没问题则可以Submit
* Date： 2023-06-15
*/
public without sharing class CCM_PurchaseOrderPreview {
   @AuraEnabled
    public static string QueryPirchaseAndItemInfo(String PurchaseOrderId,Boolean IsProtal){
        try{
            system.debug('PurchaseOrderId00>'+PurchaseOrderId);
            PurchaseOrderReview objPurchaseOrderReview = QueryDetail(PurchaseOrderId);
            if(isProtal){
                //protal端的maxStep比CRM端小1 .只计算123
                objPurchaseOrderReview.MaxStep = objPurchaseOrderReview.MaxStep == null ? '' : String.valueOf(Decimal.valueOf(objPurchaseOrderReview.MaxStep)-1);
            }else{
                objPurchaseOrderReview.MaxStep = objPurchaseOrderReview.MaxStep;
            }
            system.debug('PurchaseOrderId00>'+objPurchaseOrderReview);
            return JSON.serialize(objPurchaseOrderReview);
            
        }catch(Exception e){
            system.debug('报错信息--->'+e.getMessage()+'报错行数===>'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
        
    }
    @AuraEnabled
    public static String SubmitAndSync(String PurchaseOrderId, Double TotalDueAmount, Integer CountLine){
        //根据PurchaseOrderId查询PurchaseOrder信息
        Purchase_Order__c objPurchaseOrder = [
            SELECT Id,Freight_Term__c,Inco_term__c,Payment_Term__c,Authorized_Brand__c,Shipping_Place__c,Expected_Delivery_Date__c,
            Header_Discount__c,Header_Discount_Amount__c,Customer__c,Authorized_Brand__r.List_Price_1__c,Pricing_Date__c,
            Authorized_Brand__r.List_Price_2__c,Authorized_Brand__r.List_Price_3__c,Submit_Date__c,
            Current_Step__c,Freight_Fee__c,Total_Amount__c,Total_Value_Formal__c,Total_Value_Net__c,Total_Quantity__c 
            FROM Purchase_Order__c WHERE Id = :PurchaseOrderId
        ];
        Set<String> setProductId = new Set<String>();
        List<Purchase_Order_Item__c> lstPurchaseOrder = [
            SELECT Id,Purchase_Order__c,Product__c,Inventory__c FROM  Purchase_Order_Item__c WHERE Purchase_Order__c = :PurchaseOrderId
        ];
        for(Purchase_Order_Item__c objPurchaseOrderitem : lstPurchaseOrder){
         
            setProductId.add(objPurchaseOrderitem.Product__c);
            
        }
        //获取前端传入的PurchaseOrderItem信息--->将OrderItem信息存入数据库
        //根据Auth Brand查询下面的PriceBook查询PriceBookEntry-->产品、PriceBook唯一确认一个PriceBookName
        Set<String> setPriceBookName = new Set<String>();
        setPriceBookName.add(objPurchaseOrder.Authorized_Brand__r.List_Price_1__c);
        setPriceBookName.add(objPurchaseOrder.Authorized_Brand__r.List_Price_2__c);
        setPriceBookName.add(objPurchaseOrder.Authorized_Brand__r.List_Price_3__c);
         List<Pricebook_Entry__c> lstPriceBookEntry = [
            SELECT Id,Product__c,Start_Date__c,End_Date__c,IsActive__c,PriceBook__c, PriceBook__r.Name
            FROM Pricebook_Entry__c WHERE Product__c IN :setProductId
            AND Start_Date__c <= :objPurchaseOrder.Pricing_Date__c AND End_Date__c >= :objPurchaseOrder.Pricing_Date__c
            AND PriceBook__c IN :setPriceBookName 
        ];
        Map<String,String> mapProductDate2Name = new Map<String,String>();
        for(Pricebook_Entry__c objPriceBook : lstPriceBookEntry){
            mapProductDate2Name.put(objPriceBook.Product__c,objPriceBook.PriceBook__r.Name);
        }
        //默认全是绿灯
        Boolean isGreen = true;
        for(Purchase_Order_Item__c objPurchaseOrderitem : lstPurchaseOrder){
            objPurchaseOrderitem.List_Price_Name__c = mapProductDate2Name.get(objPurchaseOrderitem.Product__c);
            objPurchaseOrderitem.UOM__c = 'EA';
            
        }
        for(Purchase_Order_Item__c objPurchaseOrderitem : lstPurchaseOrder){
            if(objPurchaseOrderitem.Inventory__c != 'Green'){
                //有一个不为绿灯则需要审批
                isGreen = false;
                break;

            }
        }
        
        //当用户为 Inside Sales的时候。提交即同步。
        //当用户不为 Inside Sales端的时候满足条件时自动同步OrderLine<40并且Order Value<4500不需要审批。自动将订单从CRM同步到Oracle
        //inside Sales根据Role判断
       
        User objUser = [
            SELECT Id, ProfileId, Profile.Name, UserRoleId, UserRole.Name FROM User    WHERE Id = :UserInfo.getUserId()
        ];
        String SyncStatus = '';
        system.debug('objUser.Profile.Name-->'+objUser.Profile.Name);
        Set<String> InsideSalesProfile = new Set<String>(Label.Inside_Sales_Profile.split(',')) ;
        //判断UserRole的名字是否为Inside Sales--》如果是Inside Sales 直接Sync。如果不是。则判断条件
        if(InsideSalesProfile.contains(objUser.Profile.Name)){
            //表示是Inside Sales直接同步至Oracle
            //---TODO-->调用Oracle的Call Out
            // String status = CCM_PurchesOrderCallOut.pushOrderInfo(PurchaseOrderId);
            // SyncStatus = status;
                // if(status == 'Success'){
                //     //同步成功
                //     objPurchaseOrder.Status__c = CCM_Constants.PENDING_REVIEW;
                //     objPurchaseOrder.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;

                // }else{
                //     //同步失败
                //     objPurchaseOrder.Status__c = CCM_Constants.SUBMITTED;
                //     objPurchaseOrder.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
                // }
                objPurchaseOrder.Status__c = CCM_Constants.SUBMITTED;
                objPurchaseOrder.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
        }else{
            //不是Inside Sales -->校验条件
            // if(TotalDueAmount < 45000 && CountLine <40){
            //     //直接同步
            //     String status = CCM_PurchesOrderCallOut.pushOrderInfo(PurchaseOrderId);
            //     SyncStatus = status;
            //     if(status == 'Success'){
            //         //同步成功
            //         objPurchaseOrder.Status__c = CCM_Constants.PENDING_REVIEW;
            //         objPurchaseOrder.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;

            //     }else{
            //         //同步失败
            //         objPurchaseOrder.Status__c = CCM_Constants.SUBMITTED;
            //         objPurchaseOrder.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
                    
            //     }

            // }else{
            //     //状态改为Submit需要InsideSales手动同步
            //     objPurchaseOrder.Status__c = CCM_Constants.SUBMITTED;
            //     objPurchaseOrder.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
            //     //发送站内通知SendNotifyNocation
            //     SendNotifyNocation(objPurchaseOrder.Id);
            // }
            objPurchaseOrder.Status__c = CCM_Constants.SUBMITTED;
            objPurchaseOrder.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
            // SendNotifyNocation(objPurchaseOrder.Id);
        }
        objPurchaseOrder.Submit_Date__c = objPurchaseOrder.Submit_Date__c == null ? Date.today() :objPurchaseOrder.Submit_Date__c ;

        system.debug('更新的数据'+objPurchaseOrder);
        //根据purchaseOrderId查询Item更新submitDate
        List<Purchase_Order_Item__c> lstOrderItem = [
            SELECT Id,Submit_Date__c,Est_Replenish_Date__c,Inventory__c,Schedule_Ship_Date__c
            FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :PurchaseOrderId
        ];
        for(Purchase_Order_Item__c objItem : lstOrderItem){
            objItem.Submit_Date__c = objPurchaseOrder.Submit_Date__c;
        }
        update objPurchaseOrder;
        update lstOrderItem;
        
        return SyncStatus;
    }

    @AuraEnabled
    public static String sync(String purchaseOrderId) {
        String status = CCM_PurchesOrderCallOut.pushOrderInfo(purchaseOrderId);
        if(String.isNotBlank(status) && status != 'Failed') {
            Purchase_Order__c po = new Purchase_Order__c();
            po.Id = purchaseOrderId;
            po.Sync_Date__c = Date.today();
            update po;
        }
        return status;
    }
    
    @AuraEnabled
    public static void SyncOrder(String StringJsonOrderInfo){
        system.debug('接收到的参数信息--->'+StringJsonOrderInfo);
        try{
            UpdateOrderInfo objUpdateOrder = (UpdateOrderInfo)JSON.deserialize(StringJsonOrderInfo, UpdateOrderInfo.class);
            //查询数据库存在的Order信息
            Purchase_Order__c objPurchase = [
                SELECT Id,Customer_PO_Num__c,Freight_Term__c,Payment_Term__c,
                Inco_Term__c,Warehouse__c,Expected_Delivery_Date__c,Shipping_Place__c,Insurance_Fee__c,Current_Step__c,Submit_Date__c,
                Freight_Fee__c,Other_Fees__c,Is_DropShip__c,Order_Type__c,Header_Discount__c,Status__c,
                Billing_Address__c,Dropship_Address__c,Shipping_Address__c,DropShip_Type__c,
                Additional_Contact_Name__c,Additional_Contact_Phone__c,Additional_Shipping_City__c,Additional_Shipping_Country__c,
                Additional_Shipping_Postal_Code__c,Additional_Shipping_Province__c,Additional_Shipping_Street__c,
                Additional_Shipping_Street2__c
                FROM Purchase_Order__c WHERE Id = :objUpdateOrder.purchaseOrderId
            ];
            objPurchase.Customer_PO_Num__c = objUpdateOrder.customerPO;
            objPurchase.Freight_Term__c = objUpdateOrder.freightTerm;
            objPurchase.Inco_Term__c = objUpdateOrder.incoTerm;
            objPurchase.Payment_Term__c = objUpdateOrder.paymentTerm;
            objPurchase.Warehouse__c = objUpdateOrder.wareHouse;
            objPurchase.Expected_Delivery_Date__c = objUpdateOrder.exceptedDeliveryDate;
            objPurchase.Shipping_Place__c = objUpdateOrder.shippingPlace;
            objPurchase.Insurance_Fee__c = objUpdateOrder.insuranceFee;
            objPurchase.Other_Fees__c = objUpdateOrder.otherFee;
            objPurchase.Is_DropShip__c = objUpdateOrder.isDropship;
            objPurchase.Status__c = CCM_Constants.SUBMITTED;
            objPurchase.Current_Step__c = CCM_Constants.PREVIEW_SUBMIT;
            objPurchase.Submit_Date__c = Date.today();
            objPurchase.Order_Type__c = objUpdateOrder.orderType;
            objPurchase.Header_Discount__c = objUpdateOrder.headerDiscount;
            objPurchase.Status__c = objUpdateOrder.orderStatus;
            objPurchase.Billing_Address__c = objUpdateOrder.billToAddress;
            objPurchase.Shipping_Address__c = objUpdateOrder.shipToAddress;
            objPurchase.Dropship_Address__c = objUpdateOrder.dropShipAddress;
            objPurchase.DropShip_Type__c = objUpdateOrder.dropShipType;


            objPurchase.Additional_Contact_Name__c = objUpdateOrder.DropShipName;
            objPurchase.Additional_Shipping_Street__c = objUpdateOrder.DropShipAddress1;
            objPurchase.Additional_Shipping_Street2__c = objUpdateOrder.DropShipAddress2;
            objPurchase.Additional_Contact_Phone__c = objUpdateOrder.DropShipPhone;
            objPurchase.Additional_Shipping_Country__c = objUpdateOrder.DropShipCountry;
            objPurchase.Additional_Shipping_City__c = objUpdateOrder.DropShipCity;
            objPurchase.Additional_Shipping_Postal_Code__c = objUpdateOrder.DropShipZip;
            objPurchase.Additional_Shipping_Province__c = objUpdateOrder.DropShipState;

            List<UpdateOrderItemInfo> lstOrderItemInfo = objUpdateOrder.OrderItemList;
            List<String> lstOrderItemIds = new List<String>();
            for(UpdateOrderItemInfo objOrderItem : lstOrderItemInfo){
                lstOrderItemIds.add(objOrderItem.orderItemId);
            }
            List<Purchase_Order_Item__c> lstPurchaseOrderItem = [
                SELECT Id,Quantity__c,Request_Date__c,production_item__c,Product__c,Product_Model__c,
                
                Discount__c,Remarks__c FROM Purchase_Order_Item__c WHERE Id IN :lstOrderItemIds
            ];
            
            for(Purchase_Order_Item__c objPurchaseItem : lstPurchaseOrderItem){
                for(UpdateOrderItemInfo objOrderItem : lstOrderItemInfo){
                    if(objOrderItem.orderItemId == objPurchaseItem.Id){
                        objPurchaseItem.production_item__c = objOrderItem.productDescription;
                        objPurchaseItem.Product__c = objOrderItem.productId;
                        objPurchaseItem.Product_Model__c = objOrderItem.model;
                        objPurchaseItem.Quantity__c = objOrderItem.quantity;
                        objPurchaseItem.Request_Date__c = objOrderItem.requestDate;
                        objPurchaseItem.Discount__c = objOrderItem.discount;
                        objPurchaseItem.Remarks__c = objOrderItem.remark;
                    }
                }
            }
			update  lstPurchaseOrderItem;
            update objPurchase;
            //Todo调用同步接口
            
        }catch(Exception e){
            system.debug('报错信息---->'+e.getMessage()+'报错行数----->'+e.getLineNumber());
        }
        
    }
    @AuraEnabled
    public static string QueryProduct(String FilterString ,String CustomerId,Date PricingDate){
        try {
            return CCM_RequestPurchaseOrderController.GetProductInfo( FilterString , CustomerId, PricingDate);
            
        } catch (Exception e) {
            system.debug('报错信息--->'+e.getMessage()+'报错行数--->'+e.getLineNumber());
            return  null;
        }
    }
    /**
     * Author : Honey
     * Date 2023/07/31
     * Description: 临时保存文件。供前端预览文件
     */
    @AuraEnabled
    public static string uploadFileMidel( String content,String uploadFileName,String fileName){
        Map<String, String> result = new Map<String, String>();
        
        try {
            ContentVersion conVer = new ContentVersion();
            conVer.ContentLocation = 'S'; // S specify this document is in SF, use E for external files
            conVer.PathOnClient = uploadFileName; // The files name, extension is very important here which will help the file in preview.
            conVer.Title = FileName + String.valueOf(Datetime.now()); // Display name of the files
            conVer.VersionData = EncodingUtil.base64Decode(content); // converting your binary string to Blog
            insert conVer;
            ContentVersion objContentVersion = [
                SELECT Id,ContentDocumentId FROM ContentVersion WHERE Id = :conVer.Id
            ];
            system.debug('objContentVersion--->'+objContentVersion.ContentDocumentId);
            result.put('Status', 'Success');
            result.put('Message', '');
            result.put('ContentId', objContentVersion.ContentDocumentId);
            return JSON.serialize(result);
            
        } catch (Exception e) {
            system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            result.put('Status', 'Error');
            result.put('Message', e.getMessage());
            result.put('ContentId', '');                
            return JSON.serialize(result);
        }
    }
   
    @AuraEnabled
    public static void uploadFile(String purchaseOrderId,List<uploadFileInfo> lstuploadFileInfo){
        system.debug('lstuploadFileInfo--->'+lstuploadFileInfo);
        List<Purchase_Order_Attachment__c> lstdeleteAttachment = [
            SELECT Id,Purchase_Order__c,File_Id__c FROM Purchase_Order_Attachment__c WHERE Purchase_Order__c = :purchaseOrderId
        ];
        Set<String> lstContentDocumentId = new Set<String>();
        Set<String> setInsertDocument = new Set<String>();
        
        
        delete lstdeleteAttachment;
        List<Purchase_Order_Attachment__c> lstinsertPurchaseOrder = new List<Purchase_Order_Attachment__c>();
        for(uploadFileInfo objFileInfo : lstuploadFileInfo){
            Purchase_Order_Attachment__c objPurchaseAttachment = new Purchase_Order_Attachment__c();
            objPurchaseAttachment.Purchase_Order__c = purchaseOrderId;
            objPurchaseAttachment.File_Date__c = objFileInfo.fileDate;
            setInsertDocument.add(objFileInfo.contentId);
            objPurchaseAttachment.File_Id__c = objFileInfo.contentId;
            objPurchaseAttachment.File_Name__c = objFileInfo.fileName;
            objPurchaseAttachment.File_Type__c = objFileInfo.fileType;
            lstinsertPurchaseOrder.add(objPurchaseAttachment);

        }
        for(Purchase_Order_Attachment__c objAttachment : lstdeleteAttachment){
            if(!setInsertDocument.contains(objAttachment.File_Id__c)){
                lstContentDocumentId.add(objAttachment.File_Id__c);
            }
        }
        //根据AttachmentId删除文件
        List<ContentDocument> lstContent = [SELECT  Id FROM ContentDocument  WHERE Id IN :lstContentDocumentId];
        delete lstContent;

        insert lstinsertPurchaseOrder;
        
    }
    public static void SendNotifyNocation(String objectId){
        Messaging.CustomNotification notification = new Messaging.CustomNotification();
        notification.setTitle(Label.PO_APPROVAL_TITLE);
        notification.setBody('approval info');
        CustomNotificationType objNotificationType = [SELECT Id FROM CustomNotificationType WHERE DeveloperName = 'Po_Approval_Notify'];
        notification.setNotificationTypeId(objNotificationType.Id);
        // notification.setTargetId(objectId);
        // String baseUrl = URL.getSalesforceBaseUrl().toExternalForm();
        String baseUrl = 'https://ego-eu.my.salesforce.com';
        if(CCM_Service.IsSandboxOrg()) {
            baseUrl = 'https://chervoneuro--uat.sandbox.my.salesforce.com';
        }
        Map<String, Object> pageRef = new Map<String, Object>();
        pageRef.put('type', 'standard__webPage');
        pageRef.put('attributes', new Map<String, Object>());
        ((Map<String, Object>)pageRef.get('attributes')).put('url', baseUrl+'/lightning/n/PO_Detail?0.recordId=' + objectId);
        String pageRefStr = JSON.serialize(pageRef);
        notification.setTargetPageRef(pageRefStr);
        //查询所有inside Sales 
        String sendPoProfile = Label.PO_SEND_PROFILE;
        List<String> lstSendProfileIds = sendPoProfile.split(',');
        List<User> lstUserInfo   = [
            Select Id, ProfileId, Profile.Name from User where Profile.Name in :lstSendProfileIds AND IsActive = TRUE
        ];
        Set<String> setSendIds = new Set<String>();
        for(User objUser : lstUserInfo){
            setSendIds.add(objUser.Id);
        }
        system.debug('需要发送的人----->'+setSendIds);
        notification.send(setSendIds);
    }
    @AuraEnabled
    public static string queryAlertMessage(String CustomerNumber,String AlertMode ){
        try {
            return CCM_RequestPurchaseOrderController.queryAlertMessage(CustomerNumber,AlertMode);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static void refreshInventory(String purchaseOrderId){
        system.debug('purchaseOrderId--->'+purchaseOrderId);
        try {
            List<String> lstProductId = new List<String>();
            Map<String,String> mapProduct2RecordName = new Map<String,String>();
            //根据purchaseID查询purchaseOrder信息并调用查询详情
            List<Purchase_Order_Item__c> lstPurchaseItem = [
                SELECT Id,Inventory__c,Header_Record_Name__c,Purchase_Order__c,
                Purchase_Order__r.Customer__c,Purchase_Order__r.Warehouse__c,Purchase_Order__r.Pricing_Date__c,
                 Product__c,Quantity__c FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :purchaseOrderId
            ];
            Map<String,Decimal> mapProductId2Qty = new Map<String,Decimal>();
            Map<String,String> mapProductId2WearHouse = new Map<String,String>();
            for(Purchase_Order_Item__c objItem : lstPurchaseItem){
                lstProductId.add(objItem.Product__c);
                mapProductId2Qty.put(objItem.Product__c, objItem.Quantity__c);
                mapProduct2RecordName.put(objItem.Product__c, objItem.Header_Record_Name__c);
                mapProductId2WearHouse.put(objItem.Product__c,objItem.Purchase_Order__r.Warehouse__c);
            }
            Map<String,String> mapProductId2Invotory = 
            CCM_PurchaseBatchUploadController.mapProductInfo2Invotory(lstPurchaseItem[0].Purchase_Order__r.Customer__c,lstProductId,
                lstPurchaseItem[0].Purchase_Order__r.Pricing_Date__c,
                mapProductId2WearHouse,
                mapProductId2Qty,mapProduct2RecordName);
            system.debug('mapProductId2Invotory---->'+mapProductId2Invotory);
            for(Purchase_Order_Item__c objItem : lstPurchaseItem){
                objItem.Inventory__c = mapProductId2Invotory.get(objItem.Product__c);
            }
            update lstPurchaseItem;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static void CancelOrder(String purchaseOrderId){
        //通过purchaseOrderid查询
        system.debug('purchaseOrderId--->'+purchaseOrderId);
        Purchase_Order__c objPurchaseOrder = [
            SELECT Id,Status__c FROM Purchase_Order__c WHERE Id = :purchaseOrderId
        ];
        objPurchaseOrder.Status__c = 'Cancelled';
        update objPurchaseOrder;
    }
    
    
    public static PurchaseOrderReview QueryDetail(String PurchaseOrderId){  
        PurchaseOrderReview objPurchaseOrderReview = new PurchaseOrderReview();
        try {
            //根据PurchaseOrderId查询PurchaseOrder信息
            Purchase_Order__c objPurchaseOrder = [
                SELECT Id,Name ,Customer__c,Customer__r.Name, Customer__r.CurrencyIsoCode, Warehouse__c,Current_Step__c,Customer__r.Owner.Name,Total_Value_Net__c,Total_Value_Formal__c,
                Total_Amount__c,Total_Quantity__c,Header_Discount__c,Header_Discount_Amount__c,Customer__r.AccountNumber,
                Customer_PO_Num__c,Is_DropShip__c,Authorized_Brand__c,Freight_Term__c,Status__c,FileIds__c,Freight_Fee__c,
                Inco_Term__c,Expected_Delivery_Date__c,Pricing_Date__c,Billing_Address__c,SalesPerson__c,SalesPerson__r.Name,
                Billing_Address__r.Final_Address__c,Shipping_Address__c,Shipping_Address__r.Final_Address__c,Authorized_Brand__r.Brands__c,
                DropShip_Type__c,Dropship_Address__c,Dropship_Address__r.Final_Address__c,RecordType.name,VAT__c,
                Insurance_Fee__c,Other_Fees__c,recordTypeId,RecordType.DeveloperName,SelectProductJson__c,toLabel(Payment_Term__c),
                Est_OrderVolum__c,Est_Weight__c,Submit_Date__c,Shipping_Place__c,Price_List__c,Sales_Rep__c,
                Billing_Address__r.Country__c,Billing_Address__r.City__c,Billing_Address__r.Postal_Code__c,Billing_Address__r.Street_1__c,
                Billing_Address__r.Company_name_1__c,Shipping_Address__r.Company_name_1__c,Dropship_Address__r.Company_name_1__c,
                Shipping_Address__r.Country__c,Shipping_Address__r.City__c,Shipping_Address__r.Postal_Code__c,Shipping_Address__r.Street_1__c,
                Dropship_Address__r.Country__c,Dropship_Address__r.City__c,Dropship_Address__r.Postal_Code__c,Dropship_Address__r.Street_1__c,
                Additional_Shipping_Country__c,Additional_Shipping_City__c,Additional_Shipping_Postal_Code__c,Additional_Shipping_Province__c,
                Additional_Shipping_Street__c,Additional_Shipping_Street2__c,Additional_Contact_Name__c,Additional_Contact_Phone__c, Comments__c, Sync_Date__c, Sync_Status__c, Sync_Message__c
                FROM Purchase_Order__c WHERE Id = :PurchaseOrderId LIMIT  1
            ];
            AddressDetail objBillToAddress = new AddressDetail();
            objBillToAddress.City = objPurchaseOrder.Billing_Address__r.City__c;
            objBillToAddress.Country = objPurchaseOrder.Billing_Address__r.Country__c;
            objBillToAddress.PostalCode = objPurchaseOrder.Billing_Address__r.Postal_Code__c;
            objBillToAddress.Street = objPurchaseOrder.Billing_Address__r.Street_1__c;
            objBillToAddress.CompanyName = objPurchaseOrder.Billing_Address__r.Company_name_1__c;
            
            objPurchaseOrderReview.BillToAddressInfo = objBillToAddress;

            AddressDetail objshipToAddress = new AddressDetail();
            objshipToAddress.City = objPurchaseOrder.Shipping_Address__r.City__c;
            objshipToAddress.Country = objPurchaseOrder.Shipping_Address__r.Country__c;
            objshipToAddress.PostalCode = objPurchaseOrder.Shipping_Address__r.Postal_Code__c;
            objshipToAddress.CompanyName = objPurchaseOrder.Shipping_Address__r.Company_name_1__c;

            objshipToAddress.Street = objPurchaseOrder.Shipping_Address__r.Street_1__c;
            objPurchaseOrderReview.ShipToAddressInfo = objshipToAddress;

            AddressDetail objDropshipAddress = new AddressDetail();
            objDropshipAddress.City = objPurchaseOrder.Dropship_Address__r.City__c;
            objDropshipAddress.Country = objPurchaseOrder.Dropship_Address__r.Country__c;
            objDropshipAddress.CompanyName = objPurchaseOrder.Dropship_Address__r.Company_name_1__c;
            objDropshipAddress.PostalCode = objPurchaseOrder.Dropship_Address__r.Postal_Code__c;
            objDropshipAddress.Street = objPurchaseOrder.Dropship_Address__r.Street_1__c;
            objPurchaseOrderReview.DropshipAddressInfo = objDropshipAddress;
          
            objPurchaseOrderReview.DropShipName = objPurchaseOrder.Additional_Contact_Name__c;
            objPurchaseOrderReview.DropShipAddress1 = objPurchaseOrder.Additional_Shipping_Street__c;
            objPurchaseOrderReview.DropShipAddress2 = objPurchaseOrder.Additional_Shipping_Street2__c;
            objPurchaseOrderReview.DropShipPhone = objPurchaseOrder.Additional_Contact_Phone__c;
            objPurchaseOrderReview.DropShipCountry = objPurchaseOrder.Additional_Shipping_Country__c;
            objPurchaseOrderReview.DropShipZip = objPurchaseOrder.Additional_Shipping_Postal_Code__c;
            objPurchaseOrderReview.FreightCost =objPurchaseOrder.Freight_Fee__c;
            objPurchaseOrderReview.DropShipState = objPurchaseOrder.Additional_Shipping_Province__c;
            objPurchaseOrderReview.DropShipCity = objPurchaseOrder.Additional_Shipping_City__c;
            objPurchaseOrderReview.CustomerNumber = objPurchaseOrder.Customer__r.AccountNumber;
            objPurchaseOrderReview.purchaseOrderNumber = objPurchaseOrder.Name;
            String priceList = objPurchaseOrder.Price_List__c;
            if(String.isBlank(objPurchaseOrder.Price_List__c)){
                //查询Auth上的二级价格册
                List<Sales_Program__c> lstSalesProgram = [
                    SELECT Id , List_Price_1__c,List_Price_1__r.Name FROM  Sales_Program__c WHERE Customer__c = : objPurchaseOrder.Customer__c
                ];
                if(lstSalesProgram != null && lstSalesProgram.size() > 0 ){
                    priceList = lstSalesProgram[0].List_Price_1__r.Name;
                }
            }
            objPurchaseOrderReview.priceList = priceList;
            system.debug(' objPurchaseOrder.Est_OrderVolum__c;-->'+ objPurchaseOrder.Est_OrderVolum__c);
            system.debug(' objPurchaseOrder.Est_Weight__c;-->'+ objPurchaseOrder.Est_Weight__c);
            objPurchaseOrderReview.EstOrderVolume = objPurchaseOrder.Est_OrderVolum__c == null ? 0 : objPurchaseOrder.Est_OrderVolum__c.setScale(2,RoundingMode.HALF_UP);
            objPurchaseOrderReview.EstWeight = objPurchaseOrder.Est_Weight__c == null ? 0 : objPurchaseOrder.Est_Weight__c.setScale(2,RoundingMode.HALF_UP);

            objPurchaseOrderReview.customerCurrencyISOCode = objPurchaseOrder.Customer__r.CurrencyIsoCode;
            objPurchaseOrderReview.purchaseOrderId = objPurchaseOrder.Id;
            objPurchaseOrderReview.orderDate = objPurchaseOrder.submit_Date__c == null ? null : Date.valueOf(objPurchaseOrder.submit_Date__c );
            objPurchaseOrderReview.shippingPlace = objPurchaseOrder.shipping_Place__c;
            system.debug('objPurchaseOrderReview.shippingPlace--->'+objPurchaseOrderReview.shippingPlace);
            system.debug('objPurchaseOrder.SalesPerson__c---->'+objPurchaseOrder.SalesPerson__c);
            
            system.debug('objPurchaseOrder.SalesPerson__r.Name---->'+objPurchaseOrder.Customer__r.Owner.Name);

            objPurchaseOrderReview.salesPerson =objPurchaseOrder.Sales_Rep__c;
            objPurchaseOrderReview.CustomerName = objPurchaseOrder.Customer__r.Name;
            objPurchaseOrderReview.UserType = CCM_PurchaseOrderDetailController.GetUserType();
            objPurchaseOrderReview.CustomerId = objPurchaseOrder.Customer__c;
            objPurchaseOrderReview.WareHouse = objPurchaseOrder.Warehouse__c;
            system.debug('recordType--->'+objPurchaseOrder.RecordType.name);
            system.debug('recordTypeId--->'+objPurchaseOrder.RecordTypeId);
            objPurchaseOrderReview.recordTypeName = objPurchaseOrder.RecordType.Name;
            objPurchaseOrderReview.CustomerPo = objPurchaseOrder.Customer_PO_Num__c;
            objPurchaseOrderReview.IsDropship = objPurchaseOrder.Is_DropShip__c;
            objPurchaseOrderReview.AuthBrand = objPurchaseOrder.Authorized_Brand__r.Brands__c;
            objPurchaseOrderReview.PaymentTerm = objPurchaseOrder.Payment_Term__c;
            objPurchaseOrderReview.FreightTerm = objPurchaseOrder.Freight_Term__c;
            
            objPurchaseOrderReview.IncoTerm = objPurchaseOrder.Inco_Term__c +' '+ ( objPurchaseOrder.shipping_Place__c == null ? '' : objPurchaseOrder.shipping_Place__c);

            objPurchaseOrderReview.MaxStep = objPurchaseOrder.Current_Step__c;
            objPurchaseOrderReview.ExpectedDeliveryDate = objPurchaseOrder.Expected_Delivery_Date__c;
            //currentStep-->
            if(objPurchaseOrder.Status__c == CCM_Constants.DRAFT_STOCK){
                //状态为Draft CurrentStep为0
                objPurchaseOrderReview.currentStep = '0';
                objPurchaseOrderReview.OrderStatus = CCM_Constants.DRAFT_STOCK;
            }else if(objPurchaseOrder.Status__c == CCM_Constants.SUBMITTED || objPurchaseOrder.Status__c == CCM_Constants.PENDING_REVIEW){
                objPurchaseOrderReview.currentStep = '1';
                objPurchaseOrderReview.OrderStatus = CCM_Constants.SUBMITTED;

            }else if(objPurchaseOrder.Status__c == 'Cancelled'){
                objPurchaseOrderReview.currentStep = '1';
                objPurchaseOrderReview.OrderStatus = 'Cancelled';

            }else if(objPurchaseOrder.Status__c == 'review in process'){
                objPurchaseOrderReview.currentStep = '2';
                objPurchaseOrderReview.OrderStatus = 'review in process';
            }

            objPurchaseOrderReview.isSubmitted = false;
            if(objPurchaseOrder.Submit_Date__c != null) {
                objPurchaseOrderReview.isSubmitted = true;
            }

            objPurchaseOrderReview.isSynced = false;
            if(objPurchaseOrder.Sync_Date__c != null) {
                objPurchaseOrderReview.isSynced = true;
            }
            objPurchaseOrderReview.syncStatus = objPurchaseOrder.Sync_Status__c;
            objPurchaseOrderReview.syncMessage = objPurchaseOrder.Sync_Message__c;
            objPurchaseOrderReview.Status = objPurchaseOrder.Status__c;
            objPurchaseOrderReview.selectProductJson = objPurchaseOrder.SelectProductJson__c;
            objPurchaseOrderReview.PricingDate = objPurchaseOrder.Pricing_Date__c;
            objPurchaseOrderReview.BillToAddress = objPurchaseOrder.Billing_Address__r.Final_Address__c;
            objPurchaseOrderReview.ShipToAddress = objPurchaseOrder.Shipping_Address__r.Final_Address__c;
            objPurchaseOrderReview.BillToAddressId = objPurchaseOrder.Billing_Address__c;
            objPurchaseOrderReview.ShipToAddressId = objPurchaseOrder.Shipping_Address__c;
            objPurchaseOrderReview.DropshipAddress =  objPurchaseOrder.Dropship_Address__r.Final_Address__c;
            objPurchaseOrderReview.DropshipAddressId = objPurchaseOrder.Dropship_Address__c;
            objPurchaseOrderReview.DropshipType = objPurchaseOrder.DropShip_Type__c;
            objPurchaseOrderReview.recordTypeId = objPurchaseOrder.recordTypeId;
            //通过purchaseId查询Attachment得List
            List<Purchase_Order_Attachment__c> lstPurchaseAttachments = [
                SELECT Id,File_Id__c,File_Name__c,File_Type__c,Purchase_Order__c,File_Date__c FROM Purchase_Order_Attachment__c 
                WHERE Purchase_Order__c = :PurchaseOrderId
            ];
            List<AttachmentInfo> lstAttachmentInfo = new List<AttachmentInfo>();
            for(Purchase_Order_Attachment__c objPurchase : lstPurchaseAttachments){
                AttachmentInfo objAttachment = new AttachmentInfo();
                objAttachment.fileId = objPurchase.File_Id__c;
                objAttachment.fileName = objPurchase.File_Name__c;
                objAttachment.fileType = objPurchase.File_Type__c;
                objAttachment.fileDate = objPurchase.File_Date__c;
                lstAttachmentInfo.add(objAttachment);
            }
            objPurchaseOrderReview.lstAttachmentInfo = lstAttachmentInfo;
           
            
            
            objPurchaseOrderReview.TotalValue = objPurchaseOrder.Total_Value_Formal__c == null ? 0 : objPurchaseOrder.Total_Value_Formal__c.setScale(2,RoundingMode.HALF_UP);
            objPurchaseOrderReview.HeaderDiscount = objPurchaseOrder.Header_Discount__c == null ? 0 : objPurchaseOrder.Header_Discount__c.setScale(2,RoundingMode.HALF_UP);
            objPurchaseOrderReview.HeaderDiscountAmount = objPurchaseOrder.Header_Discount_Amount__c == null ? 0 : objPurchaseOrder.Header_Discount_Amount__c.setScale(2,RoundingMode.HALF_UP);
            objPurchaseOrderReview.TotalValueNet = objPurchaseOrder.Total_Value_Net__c ==null ? 0 : objPurchaseOrder.Total_Value_Net__c.setScale(2,RoundingMode.HALF_UP);
            
            objPurchaseOrderReview.InsuranceFee = objPurchaseOrder.Insurance_Fee__c == null ? 0 : objPurchaseOrder.Insurance_Fee__c.setScale(2,RoundingMode.HALF_UP);
            objPurchaseOrderReview.OtherFee = objPurchaseOrder.Other_Fees__c == null ? 0 : objPurchaseOrder.Other_Fees__c.setScale(2,RoundingMode.HALF_UP);
            objPurchaseOrderReview.VAT = objPurchaseOrder.VAT__c == null ? 0 : objPurchaseOrder.VAT__c.setScale(2,RoundingMode.HALF_UP);
            objPurchaseOrderReview.TotalDueAmount = objPurchaseOrder.Total_Amount__c == null ? 0 : objPurchaseOrder.Total_Amount__c.setScale(2,RoundingMode.HALF_UP);
            objPurchaseOrderReview.Comments = objPurchaseOrder.Comments__c;
            
            List<String> lstAllproductIds = new List<String>();
            //根据PurchaseId查询子订单信息
            List<Purchase_Order_Item__c> lstOrderItem = [
                SELECT Id,Name,Product__c,Product__r.Item_Description_DE__c,Request_Date__c,Pricing_Date__c,
                Product__r.Item_Description_EN__c,Product__r.Order_Model__c,Quantity__c,UOM__c,Sales_Price__c,
                Product__r.Retail_Unit_Weight__c ,Product__r.Retail_Unit_Volume__c,Fallshortofthreshold__c,
                Stand_Discount__c,Application_Method__c,Stand_Discount_Value__c,
                Submit_Date__c,Est_Replenish_Date__c,Inventory__c,Schedule_Ship_Date__c,
                // add haibo (获取product record type -- Product__r.RecordType_Name__c)
                List_Price__c,Discount__c,Unit_Price__c,Total_Net_Price__c,Remarks__c,Product__r.RecordType_Name__c FROM Purchase_Order_Item__c WHERE Purchase_Order__c = :PurchaseOrderId
                AND Fallshortofthreshold__c != TRUE
            ];
            system.debug('lstOrderItem-->'+lstOrderItem);
            if(lstOrderItem == null || lstOrderItem.size()==0){
                return objPurchaseOrderReview;
            }
            objPurchaseOrderReview.CountLine = lstOrderItem.size();
            //遍历OrderItem.获取产品Id用于查询Tools信息--存放返回前端的Item信息
            Set<String> setProductId = new Set<String>();
            for(Purchase_Order_Item__c objItem : lstOrderItem){
                system.debug('Test--->'+objItem.Sales_Price__c);
                setProductId.add(objItem.Product__c);
                lstAllproductIds.add(objItem.Product__c);
            }
            
            List<PurchaseOrderItemReview> lstPurchaseOrderItem = new List<PurchaseOrderItemReview>();
            //根据产品信息查询Kit的Tools信息---> 
            List<Kit_Item__c> matchingKitItems = [SELECT VK_Product__c,VK_Product__r.Item_Description_DE__c,VK_Product__r.Order_Model__c,
            
                                                  VK_Product__r.Item_Description_EN__c ,Kit__c,Quantity__c   FROM Kit_Item__c  WHERE Kit__c IN :setProductId];
            system.debug('matchingKitItems--->'+matchingKitItems);
            //遍历Kit获取创建ProductId2Tools
            Map<String,List<Kit_Item__c>> mapProduct2Kit = new Map<String,List<Kit_Item__c>>();
            List<String> lstKitProductIds = new List<String>();

            for(Kit_Item__c objKitItem : matchingKitItems){
                List<Kit_Item__c> lstKitItemByProduct = mapProduct2Kit.containsKey(objKitItem.Kit__c)?
                    mapProduct2Kit.get(objKitItem.Kit__c) : new List<Kit_Item__c>();
                lstKitItemByProduct.add(objKitItem);
                mapProduct2Kit.put(objKitItem.Kit__c,lstKitItemByProduct);
                lstKitProductIds.add(objKitItem.VK_Product__c);
            }
            //调用查询红绿灯方法
            
            Map<String,Map<String,Object>> mapProductId2Inventory =   CCM_RequestPurchaseOrderController.CheckInvotary
                    (objPurchaseOrderReview.CustomerId,lstKitProductIds,null,objPurchaseOrderReview.PricingDate,objPurchaseOrderReview.purchaseOrderId);
            
            Double EstVolum = 0;
            Double EstWeight = 0;
            //遍历Item获取产品信息
            Map<String,Decimal> mapProductId2Qty = new Map<String,Decimal>();
            for(Purchase_Order_Item__c objItem : lstOrderItem){
                PurchaseOrderItemReview objPurchaseOrderItem = new PurchaseOrderItemReview();
                objPurchaseOrderItem.No = objItem.Name;
                mapProductId2Qty.put(objItem.Product__c,objItem.Quantity__c);
                //判断当前用户环境
                if(UserInfo.getLanguage().equals(CCM_Constants.EN)){
                    objPurchaseOrderItem.ProductDescription = objItem.Product__r.Item_Description_EN__c;
                }else if(UserInfo.getLanguage().equals(CCM_Constants.DE)){
                    objPurchaseOrderItem.ProductDescription = objItem.Product__r.Item_Description_DE__c;
                }
                objPurchaseOrderItem.Model = objItem.Product__r.Order_Model__c;
                objPurchaseOrderItem.standDiscount = objItem.Stand_Discount_Value__c;
                system.debug('objItem.Stand_Discount__c--->'+objItem.Stand_Discount_Value__c);
                system.debug('objItem.Application_Method__c--->'+objItem.Application_Method__c);
                objPurchaseOrderItem.applicationMethod = objItem.Application_Method__c;
                objPurchaseOrderItem.RequestDate = objItem.Request_Date__c;
                objPurchaseOrderItem.Pricingdate = objItem.Pricing_Date__c;
                objPurchaseOrderItem.Fallshortofthreshold = objItem.Fallshortofthreshold__c;
                objPurchaseOrderItem.UOM = objItem.UOM__c;
                objPurchaseOrderItem.Qty = objItem.Quantity__c;
                objPurchaseOrderItem.ListPrice = objItem.List_Price__c == null ? 0 : objItem.List_Price__c.setScale(2,RoundingMode.HALF_UP);
                objPurchaseOrderItem.Discount = objItem.Discount__c == null ? 0 : objItem.Discount__c;
                objPurchaseOrderItem.UnitNetPrice = objItem.Unit_Price__c == null ? 0 : objItem.Unit_Price__c.setScale(2,RoundingMode.HALF_UP);
                objPurchaseOrderItem.TotalNetPrice= objItem.Total_Net_Price__c == null ?  0: objItem.Total_Net_Price__c.setScale(2,RoundingMode.HALF_UP);
                objPurchaseOrderItem.Remark = objItem.Remarks__c;
                objPurchaseOrderItem.orderDate = objPurchaseOrder.Submit_Date__c;
                objPurchaseOrderItem.salesPrice = objItem.Sales_Price__c;
                system.debug('objItem.Sales_Price__c--->'+objItem.Sales_Price__c);
                objPurchaseOrderItem.Inventory = objItem.Inventory__c;
          
                objPurchaseOrderItem.scheduShipDate = objItem.Schedule_Ship_Date__c;
                
                EstVolum += 
                    objItem.Product__r.Retail_Unit_Volume__c == null ? 0 : objItem.Product__r.Retail_Unit_Volume__c;
                EstWeight += objItem.Product__r.Retail_Unit_Weight__c == null ? 0 : objItem.Product__r.Retail_Unit_Weight__c;
                // add haibo (获取product record type -- Product__r.RecordType_Name__c)
                objPurchaseOrderItem.productRecordType = objItem.Product__r.RecordType_Name__c;
                system.debug('mapProduct2Kit--->'+mapProduct2Kit);
                if(mapProduct2Kit.containsKey(objItem.Product__c)){
                    List<ProductTools> lstProductTools = new List<ProductTools>();
                    //表示是Kit类型的产品，包含Tools。遍历Tools
                    Integer i = 1;
                    for(Kit_Item__c objKitItem : mapProduct2Kit.get(objItem.Product__c)){
                        lstAllproductIds.add(objKitItem.VK_Product__c);
                        ProductTools objProductTools = new ProductTools();
                        objProductTools.No = objItem.Name+'.'+i;
                        i++;
                        if(UserInfo.getLanguage().equals(CCM_Constants.EN)){
                            objProductTools.ProductDescription = objKitItem.VK_Product__r.Item_Description_EN__c;
                        }else if(UserInfo.getLanguage().equals(CCM_Constants.DE)){
                            objProductTools.ProductDescription = objKitItem.VK_Product__r.Item_Description_DE__c;
                        }
                        objProductTools.Model = objKitItem.VK_Product__r.Order_Model__c;
                        objProductTools.RequestDate = objItem.Request_Date__c;
                        objProductTools.Pricingdate = objItem.Pricing_Date__c;
                        
                        objProductTools.Qty = (objKitItem.Quantity__c)*(objItem.Quantity__c);
                        objProductTools.ListPrice =0;
                        objProductTools.Discount =0;
                        objProductTools.UnitNetPrice =0;
                        objProductTools.TotalNetPrice =0;
                        objProductTools.Remark ='';
                        system.debug('objPurchaseOrder.Submit_Date__c--->'+objPurchaseOrder.Submit_Date__c);
                        objProductTools.orderDate = objPurchaseOrder.Submit_Date__c;
                        Map<String,Object> mapValue2Inventory = mapProductId2Inventory.get(objKitItem.VK_Product__c);
                        String InventoryTools = '';
                        if(mapValue2Inventory.get('CurrentStatus') ==  CCM_Constants.GREEN_LIGHT){
                            //表示是可以变化的
                            Decimal maxGreen = (Decimal)mapValue2Inventory.get('MaxGreenLight');
                            Decimal maxYellow = (Decimal)mapValue2Inventory.get('MaxYellowLight');
                            if(objProductTools.Qty <=  maxGreen){
                                InventoryTools = 'Green';
                            }else if(objProductTools.Qty <=  maxYellow){
                                InventoryTools = 'Yellow';
                            }else{
                                InventoryTools = 'Red';
                            }


                        }else if(mapValue2Inventory.get('CurrentStatus') ==  CCM_Constants.YELLOW_LIGHT){
                            InventoryTools = 'Yellow';
                        }else{
                            InventoryTools = 'Red';
                        }
                        system.debug('InventoryTools---->'+InventoryTools);
                        objProductTools.Inventory = InventoryTools;
                        system.debug('objItem.Schedule_Ship_Date__c--->'+objItem.Schedule_Ship_Date__c);
                        objProductTools.scheduShipDate = objItem.Schedule_Ship_Date__c;
                        lstProductTools.add(objProductTools);
                    }
                    objPurchaseOrderItem.lstProductTools = lstProductTools;
                }
                
                lstPurchaseOrderItem.add(objPurchaseOrderItem);
                
            }
            system.debug('lstPurchaseOrderItem--->'+lstPurchaseOrderItem);
            objPurchaseOrderReview.lstPurchaseOrderItem = lstPurchaseOrderItem;
            objPurchaseOrder.Est_OrderVolum__c = EstVolum;
            objPurchaseOrder.Est_Weight__c = EstWeight;
            objPurchaseOrderReview.lstDetailProductINfo = queryOtherInfo(lstAllproductIds, objPurchaseOrderReview,
            mapProductId2Qty,lstOrderItem);
            system.debug('lstDetailProductINfo--->'+objPurchaseOrderReview.lstDetailProductINfo);
            update objPurchaseOrder;
        } catch (Exception e) {
            system.debug('报错信息--->'+e.getMessage()+'报错行数--->'+e.getLineNumber());
        }
        return objPurchaseOrderReview;
    }
    /**
     * lstAllproductIds 所有产品的Id.包括Kit下面的Tools
     * objPurchaseOrderReview PO订单的信息。主要包括CustomerId,recordId,wearHouse,pricing Date
     * Kit的ProductId到Tools的映射
     * lstPurchaseOrderItem: 所有Item
    */
    public static List<DetailProductInfoAndAll> queryOtherInfo(List<String> lstAllproductIds,PurchaseOrderReview objPurchaseOrderReview,
            Map<String,Decimal> mapProductId2Qty,List<Purchase_Order_Item__c> lstPurchaseOrderItem){
        List<String> lstItemProducts = new List<String>();
        for(Purchase_Order_Item__c objItem : lstPurchaseOrderItem){
            lstItemProducts.add(objItem.Product__c);
        }
        List<DetailProductInfoAndAll> lstAllDetailInfo = new List<DetailProductInfoAndAll>();
        Map<String,Map<String,Object>> mapProductId2InventoryInfo = 
        CCM_RequestPurchaseOrderController.CheckInvotary(objPurchaseOrderReview.CustomerId,lstAllproductIds,null,objPurchaseOrderReview.PricingDate,objPurchaseOrderReview.purchaseOrderId);
        //查询产品信息
        // List<Product2> lstProductInfo = [
        //     SELECT Id,Order_Model__c,Name,Units_Per_Inner_BOX_EA__c ,Units_Per_Pallet_EA__c,Units_Per_Master_Carton_EA__c ,RecordTypeId, RecordType.Name
        //     FROM Product2 WHERE Id IN : lstItemProducts
        // ];

        // 23-12-07 补充查询 Pallet_Item__c, MPQ__c
        List<Product2> lstProductInfo = [
            SELECT Id,Order_Model__c,Name,Units_Per_Inner_BOX_EA__c ,Units_Per_Pallet_EA__c,Units_Per_Master_Carton_EA__c ,RecordTypeId, RecordType.Name, Pallet_Item__c, MPQ__c
            FROM Product2 WHERE Id IN : lstItemProducts
        ];
        
        //通过Customer查询SalesChannel
        Account objAccount = [
            SELECT Id,Sales_Channel__c FROM Account WHERE Id = :objPurchaseOrderReview.CustomerId
        ];
        //查询Kit下面的Item信息
        
        Set<String> setItems = new Set<String>();
        List<Product2> filteredProducts = new List<Product2>();
        Map<String,Decimal> mapProduct2QtyTools = new Map<String,Decimal>();
        List<Kit_Item__c> matchingKitItems = [SELECT Product__c,Kit__c,Quantity__c,VK_Product__c   FROM Kit_Item__c  
                                                  WHERE Kit__c IN :lstItemProducts AND VK_Product__c != null];
        Map<String,Map<String,Decimal>> mapProductId2ToolsQty = new Map<String,Map<String,Decimal>>();
        //将Kit放在里面
        Map<String,List<Kit_Item__c>> mapProductId2Kit = new Map<String,List<Kit_Item__c>>();
        Map<String,Product2> mapProductId2Info = new Map<String,Product2>();
        system.debug('matchingKitItems-->'+matchingKitItems);
        if(matchingKitItems != null && matchingKitItems.size()>0){
            for(Kit_Item__c objKit : matchingKitItems){
                setItems.add(objKit.VK_Product__c);
                mapProduct2QtyTools.put(objKit.VK_Product__c,objKit.Quantity__c);
                List<Kit_Item__c> lstKitItemByProductId = mapProductId2Kit.containsKey(objKit.Kit__c)?
                    mapProductId2Kit.get(objKit.Kit__c) :  new List<Kit_Item__c> () ;
                    lstKitItemByProductId.add(objKit);
                    mapProductId2Kit.put(objKit.Kit__c,lstKitItemByProductId);
            
            }
            filteredProducts = [SELECT Id,Item_Description_DE__c,InvotoryInfo__c,Item_Description_EN__c,Full_Pallet_Quantity__c,Order_Model__c,Units_Per_Master_Carton_EA__c
                                FROM Product2  WHERE Id IN :setItems];
            for(Product2 objProdct : filteredProducts){
                mapProductId2Info.put(objProdct.Id, objProdct);
            }
        }
        system.debug('filteredProducts--->'+filteredProducts);
        system.debug('mapProductId2Kit--->'+mapProductId2Kit);
        Map<String,List<Product2>> mapKitId2ToolsId = new  map<String,List<Product2>>();
        for(String KitId : mapProductId2Kit.keySet()){
            for(Kit_Item__c objKit : mapProductId2Kit.get(KitId)){
                List<Product2> lstTooIdByKit = mapKitId2ToolsId.containsKey(objKit.Kit__c)?
                    mapKitId2ToolsId.get(objKit.Kit__c) : new List<Product2>();
                    lstTooIdByKit.add(mapProductId2Info.get(objKit.VK_Product__c));
                    mapKitId2ToolsId.put(objKit.Kit__c, lstTooIdByKit);

            }

        }
        system.debug('mapKitId2ToolsId--->'+mapKitId2ToolsId);

        Map<String,Decimal> mapProductId2DoubleQty = new Map<String,Decimal>();
        for(String kitId : mapKitId2ToolsId.keySet()){
            Decimal doubleMasterCartonPallent =  0;
            for(Product2 objProduct : mapKitId2ToolsId.get(kitId)){
                doubleMasterCartonPallent += objProduct.Units_Per_Master_Carton_EA__c == null ? 0 : objProduct.Units_Per_Master_Carton_EA__c;
            }
            mapProductId2DoubleQty.put(kitId, doubleMasterCartonPallent);
        }
        
        //遍历一个Kit下的Item
       
        // Map<String,Decimal> mapProductId2DefaultQty = new Map<String,Decimal>();
        // 23-12-07 
        Map<String, String> productQtyMap = CCM_RequestPurchaseOrderController.calculateQtyForProducts(objAccount.Sales_Channel__c, objPurchaseOrderReview.WareHouse, lstProductInfo, mapProductId2DoubleQty, 0.0);
        Map<String,String> mapProductId2DefaultQty = new Map<String,String>();
        for(Product2 objProduct : lstProductInfo){
           system.debug('objProduct--->'+objProduct);
           system.debug('objPurchaseOrderReview.recordTypeName--->'+objPurchaseOrderReview.recordTypeName);
           String recordTypeName = '';
           if(objPurchaseOrderReview.recordTypeName == 'Regular Order'){
                recordTypeName = CCM_Constants.REGULAR_ORDER;
           }else{
            recordTypeName = CCM_Constants.PRE_SEASON_ORDER;
           }
           system.System.debug('mapProductId2DoubleQty--->'+mapProductId2DoubleQty);
            // Decimal DefaultQty = CCM_RequestPurchaseOrderController.CaulateQty( objAccount.Sales_Channel__c, objProduct.Units_Per_Pallet_EA__c, 
            // objProduct.Units_Per_Master_Carton_EA__c , objProduct.Units_Per_Inner_BOX_EA__c, objProduct.RecordType.Name,
            //     objPurchaseOrderReview.WareHouse, (Double)mapProductId2DoubleQty.get(objProduct.Id));

            // 23-12-07 
            // String DefaultQty = CCM_RequestPurchaseOrderController.CaulateQty( objAccount.Sales_Channel__c, objProduct.Units_Per_Pallet_EA__c, 
            // objProduct.Units_Per_Master_Carton_EA__c , objProduct.Units_Per_Inner_BOX_EA__c, objProduct.RecordType.Name,
            //     objPurchaseOrderReview.WareHouse, (Double)mapProductId2DoubleQty.get(objProduct.Id)); 

            // String DefaultQty = CCM_RequestPurchaseOrderController.CaulateQty(objAccount.Sales_Channel__c, objProduct.RecordType.Name, objPurchaseOrderReview.WareHouse, objProduct.Pallet_Item__c,
            //     objProduct.Units_Per_Pallet_EA__c, objProduct.Units_Per_Master_Carton_EA__c , objProduct.Units_Per_Inner_BOX_EA__c,
            //     mapProductId2DoubleQty.get(objProduct.Id), objProduct.MPQ__c, 0.0);
            String DefaultQty = '';
            if(productQtyMap.containsKey(objProduct.Id)) {
                DefaultQty = productQtyMap.get(objProduct.Id);
            }
            mapProductId2DefaultQty.put(objProduct.Id, DefaultQty);
        }
        system.debug('mapProductId2DefaultQty--->'+mapProductId2DefaultQty);
        for(Purchase_Order_Item__c objPurchaseOrderItem : lstPurchaseOrderItem){
            system.debug('objPurchaseOrderItem--->'+objPurchaseOrderItem);
            DetailProductInfoAndAll objDetailInfo = new DetailProductInfoAndAll();
            String productId = objPurchaseOrderItem.Product__c;
            system.debug('productId--->'+productId);
            Map<String,Object> mapInventoryInfo = mapProductId2InventoryInfo.get(productId);
            String CurrentStatus = (String)mapInventoryInfo.get('CurrentStatus');
            Decimal MaxGreenLight = (Decimal)mapInventoryInfo.get('MaxGreenLight');
            Decimal MaxYellowLight = (Decimal)mapInventoryInfo.get('MaxYellowLight');
            objDetailInfo.standDiscount = objPurchaseOrderItem.Stand_Discount_Value__c;
            objDetailInfo.applicationMethod = objPurchaseOrderItem.Application_Method__c;
            system.debug('standDiscount===>'+objPurchaseOrderItem.Stand_Discount_Value__c);
            system.debug('applicationMethod===>'+objPurchaseOrderItem.Application_Method__c);
            if(mapKitId2ToolsId.containsKey(productId)){
                //表示是Kit类型
                List<Product2> lstFilterProduct = mapKitId2ToolsId.get(productId);
                for(Product2  objProduct : lstFilterProduct){
                    Map<String,Object> mapInventoryInfoTools = mapProductId2InventoryInfo.get(objProduct.Id);
                    objProduct.InvotoryInfo__c = (String)mapInventoryInfoTools.get('CurrentStatus');
                    objProduct.MaxGreenLight__c = (Decimal)mapInventoryInfoTools.get('MaxGreenLight');
                    objProduct.MaxYellowLight__c = (Decimal)mapInventoryInfoTools.get('MaxYellowLight');
                    objProduct.Units_Per_Master_Carton_EA__c = mapProduct2QtyTools.get(objProduct.Id) * (Integer)mapProductId2Qty.get(productId);
                }
                objDetailInfo.filteredProducts = lstFilterProduct;
                // objDetailInfo.defaultQty = (Integer)mapProductId2DefaultQty.get(productId);
                // 23-12-07
                objDetailInfo.defaultQty = mapProductId2DefaultQty.get(productId);
                objDetailInfo.Qty =  (Integer)mapProductId2Qty.get(productId);
                objDetailInfo.CurrentStatus = CurrentStatus;
                objDetailInfo.productId = productId;
                objDetailInfo.MaxGreenLight = MaxGreenLight;
                objDetailInfo.MaxYellowLight = MaxYellowLight;
                objDetailInfo.Discount = objPurchaseOrderItem.discount__c;
                objDetailInfo.listPrice = objPurchaseOrderItem.List_Price__c;
                objDetailInfo.SalesPrice = objPurchaseOrderItem.Sales_Price__c;
               
                objDetailInfo.unitNetPrice = objPurchaseOrderItem.Unit_Price__c;
                objDetailInfo.TotalNetPrice = objPurchaseOrderItem.Total_Net_Price__c;
                objDetailInfo.Model = objPurchaseOrderItem.Product__r.Order_Model__c;
                if(UserInfo.getLanguage().equals(CCM_Constants.EN)){
                    objDetailInfo.ProductDescription = objPurchaseOrderItem.Product__r.Item_Description_EN__c;
                }else if(UserInfo.getLanguage().equals(CCM_Constants.DE)){
                    objDetailInfo.ProductDescription = objPurchaseOrderItem.Product__r.Item_Description_DE__c;
                }
                lstAllDetailInfo.add(objDetailInfo);

            }else{
                //表示不是Kit类型
                // 23-12-07
                objDetailInfo.defaultQty = mapProductId2DefaultQty.get(productId);
                objDetailInfo.productId = productId;
                objDetailInfo.Qty =  (Integer)mapProductId2Qty.get(productId);
                objDetailInfo.CurrentStatus = CurrentStatus;
                objDetailInfo.MaxGreenLight = MaxGreenLight;
                objDetailInfo.MaxYellowLight = MaxYellowLight;
                objDetailInfo.Discount = 0;
                objDetailInfo.listPrice = objPurchaseOrderItem.List_Price__c;
                objDetailInfo.SalesPrice = objPurchaseOrderItem.Sales_Price__c;
                objDetailInfo.unitNetPrice = objPurchaseOrderItem.Unit_Price__c;
                objDetailInfo.TotalNetPrice = objPurchaseOrderItem.Total_Net_Price__c;
                objDetailInfo.Model = objPurchaseOrderItem.Product__r.Order_Model__c;
                if(UserInfo.getLanguage().equals(CCM_Constants.EN)){
                    objDetailInfo.ProductDescription = objPurchaseOrderItem.Product__r.Item_Description_EN__c;
                }else if(UserInfo.getLanguage().equals(CCM_Constants.DE)){
                    objDetailInfo.ProductDescription = objPurchaseOrderItem.Product__r.Item_Description_DE__c;
                }
                lstAllDetailInfo.add(objDetailInfo);
            }
        }
        return   lstAllDetailInfo;
        
    }
    //创建一个需要返回的对象
    public Class PurchaseOrderReview{
        //PurchaseOrder的数据
        @AuraEnabled public String purchaseOrderId{get;set;}
        @AuraEnabled public String purchaseOrderNumber{get;set;}
        @AuraEnabled public String CustomerName{get;set;}
        @AuraEnabled public String salesPerson{get;set;}
        @AuraEnabled public String CustomerId{get;set;}
        @AuraEnabled public String CustomerNumber{get;set;}
        @AuraEnabled public String recordTypeId{get;set;}
        @AuraEnabled public String recordTypeName{get;set;}
        @AuraEnabled public String WareHouse{get;set;}
        @AuraEnabled public String CustomerPo{get;set;}
        @AuraEnabled public Boolean IsDropship{get;set;}
        @AuraEnabled public String AuthBrand{get;set;}
        @AuraEnabled public String PaymentTerm{get;set;}
        @AuraEnabled public String FreightTerm{get;set;}
        @AuraEnabled public String IncoTerm{get;set;}
        @AuraEnabled public Date ExpectedDeliveryDate{get;set;}
        @AuraEnabled public Date PricingDate{get;set;}
        @AuraEnabled public Date orderDate{get;set;}
        @AuraEnabled public String BillToAddress{get;set;}
        @AuraEnabled public String BillToAddressId{get;set;}
        @AuraEnabled public String ShipToAddress{get;set;}
        @AuraEnabled public String ShipToAddressId{get;set;}
        @AuraEnabled public String DropshipType{get;set;}
        @AuraEnabled public String DropshipAddress{get;set;}
        @AuraEnabled public String DropshipAddressId{get;set;}
        @AuraEnabled public String selectProductJson{get;set;}
        @AuraEnabled public String CurrentStep{get;set;}
        
        @AuraEnabled public String MaxStep{get;set;}
        @AuraEnabled public String Status{get;set;}
        @AuraEnabled public String OrderStatus{get;set;}
        @AuraEnabled public String priceList{get;set;}
        @AuraEnabled public String shippingPlace{get;set;}
       
        @AuraEnabled public Decimal EstOrderVolume{get;set;}
        @AuraEnabled public Decimal EstWeight{get;set;}
        @AuraEnabled public List<PurchaseOrderItemReview> lstPurchaseOrderItem{get;set;}
        
        
        @AuraEnabled public Decimal TotalValue{get;set;}
        @AuraEnabled public Decimal HeaderDiscount{get;set;}
        @AuraEnabled public Decimal HeaderDiscountAmount{get;set;}
        @AuraEnabled public Decimal TotalValueNet{get;set;}
        @AuraEnabled public Decimal FreightCost{get;set;}
        @AuraEnabled public Decimal InsuranceFee{get;set;}
        @AuraEnabled public Decimal OtherFee{get;set;}
        @AuraEnabled public Decimal VAT{get;set;}
        @AuraEnabled public Decimal TotalDueAmount{get;set;}
        @AuraEnabled public String Comments{get;set;}
        
        @AuraEnabled public Integer CountLine{get;set;}

        //附件的Id
        @AuraEnabled public List<AttachmentInfo> lstAttachmentInfo {get;set;}

        //添加AddressInfo 
        @AuraEnabled public AddressDetail BillToAddressInfo{get;set;}
        @AuraEnabled public AddressDetail ShipToAddressInfo{get;set;}
        @AuraEnabled public AddressDetail DropshipAddressInfo{get;set;}


        @AuraEnabled public String DropShipName {get;set;}
        @AuraEnabled public String DropShipAddress1 {get;set;}
        @AuraEnabled public String DropShipAddress2 {get;set;}
        @AuraEnabled public String DropShipPhone {get;set;}
        @AuraEnabled public String DropShipCountry {get;set;}
        @AuraEnabled public String DropShipCity{get;set;}
        @AuraEnabled public String DropShipZip {get;set;}
        @AuraEnabled public String DropShipState {get;set;}
        @AuraEnabled public String UserType {get;set;}
        @AuraEnabled public List<DetailProductInfoAndAll> lstDetailProductINfo {get;set;}
        @AuraEnabled public String customerCurrencyISOCode {get;set;}
        @AuraEnabled public Boolean isSubmitted {get;set;}
        @AuraEnabled public Boolean isSynced {get;set;}
        @AuraEnabled public String syncStatus {get;set;}
        @AuraEnabled public String syncMessage {get;set;}
    }
    public Class DetailProductInfoAndAll{
        //子库信息
        public List<Product2> filteredProducts {get;set;}
        //默认数量信息
        // 23-12-07
        // public Integer defaultQty{get;set;}
        public String defaultQty{get;set;}
        //下单数据
        public Integer Qty{get;set;}
        //库存信息
        //String Invotory{get;set;}
        //绿灯表示可改变--->黄灯，红灯表示不可变
        public String CurrentStatus{get;set;}
        public String productId{get;set;}
        public Decimal MaxYellowLight{get;set;}
        public Decimal MaxGreenLight{get;set;}
        public String Model{get;set;}
        
        public String OriginalProductName{get;set;}
        public String ProductDescription{get;set;}
        public Decimal standDiscount{get;set;}
        public String applicationMethod{get;set;}
        
        
        //原价
        public Decimal listPrice{get;set;}
        //折扣
        public Double Discount{get;set;}
        //二级价格册
        public Decimal SalesPrice{get;set;}
        //售出价
        public Decimal unitNetPrice{get;set;}
        //总价
        public Double TotalNetPrice{get;set;}
    }
    public class RefreshInfo{
        
        @AuraEnabled public String purchaseOrderId{get;set;}
        @AuraEnabled public String wareHouse{get;set;}
        @AuraEnabled public String pricingDate{get;set;}
        @AuraEnabled public String csutomerId{get;set;}
        @AuraEnabled public Map<String,Decimal> mapProductId2Qty{get;set;}

    }
    public class uploadFileInfo{
        @AuraEnabled public String contentId{get;set;}
        @AuraEnabled public String fileType{get;set;}
        @AuraEnabled public String fileName{get;set;}
        @AuraEnabled public Date fileDate{get;set;}
    }
    public class AttachmentInfo{
        @AuraEnabled public String fileId{get;set;}
        @AuraEnabled public String fileName{get;set;}
        @AuraEnabled public String fileType{get;set;}
        @AuraEnabled public Date fileDate{get;set;}
    }
    public Class AddressDetail{
        public String Country {get;set;}
        public String City {get;set;}
        public String Street {get;set;}
        public String PostalCode {get;set;}
        public String CompanyName {get;set;}
    }
    //子订单对象
    public Class PurchaseOrderItemReview{
        @AuraEnabled  public Decimal standDiscount{get;set;}
        @AuraEnabled  public Boolean Fallshortofthreshold{get;set;}
        @AuraEnabled public String applicationMethod{get;set;}
        @AuraEnabled public String No{get;set;}
        @AuraEnabled public String ProductDescription{get;set;}
        @AuraEnabled public String Model{get;set;}
        @AuraEnabled public Date RequestDate{get;set;}
        @AuraEnabled public Date Pricingdate{get;set;}
        @AuraEnabled public Decimal Qty{get;set;}
        @AuraEnabled public String UOM{get;set;}
        @AuraEnabled public Decimal ListPrice{get;set;}
        @AuraEnabled public Decimal salesPrice{get;set;}
       
        
        @AuraEnabled public Decimal Discount{get;set;}
        @AuraEnabled public Decimal UnitNetPrice{get;set;}
        @AuraEnabled public Decimal TotalNetPrice{get;set;}
        @AuraEnabled public String Remark{get;set;}
        @AuraEnabled public List<ProductTools> lstProductTools{get;set;}


        @AuraEnabled public Date orderDate{get;set;}
        @AuraEnabled public String Inventory{get;set;}
        
       
        @AuraEnabled public Date scheduShipDate{get;set;}
        // add haibo (获取product record type -- Product__r.RecordType_Name__c)
        @AuraEnabled public String productRecordType;
        
    }
    //子订单的Tools信息
    public Class ProductTools{
        @AuraEnabled public String No{get;set;}
        @AuraEnabled public String ProductDescription{get;set;}
        @AuraEnabled public String Model{get;set;}
        @AuraEnabled public Date RequestDate{get;set;}
        @AuraEnabled public Date Pricingdate{get;set;}
        @AuraEnabled public Decimal Qty{get;set;}
        @AuraEnabled public Decimal ListPrice{get;set;}
        
        @AuraEnabled public Decimal Discount{get;set;}
        @AuraEnabled public Decimal UnitNetPrice{get;set;}
        @AuraEnabled public Decimal TotalNetPrice{get;set;}
        @AuraEnabled public String Remark{get;set;}

        @AuraEnabled public Date orderDate{get;set;}
        @AuraEnabled public String Inventory{get;set;}
        
       
        @AuraEnabled public Date scheduShipDate{get;set;}
        
    }
    
    //
    public Class UpdateOrderInfo{
        @AuraEnabled public String purchaseOrderId {get;set;}
        @AuraEnabled public String customerPO {get;set;}
        @AuraEnabled public String freightTerm {get;set;}
        @AuraEnabled public String incoTerm {get;set;}
        @AuraEnabled public String paymentTerm {get;set;}
        @AuraEnabled public String wareHouse {get;set;}
        @AuraEnabled public Date exceptedDeliveryDate {get;set;}
        @AuraEnabled public String shippingPlace {get;set;}
        
        @AuraEnabled public Decimal insuranceFee {get;set;}
        @AuraEnabled public Decimal otherFee {get;set;}
        @AuraEnabled public Boolean isDropship {get;set;}
        @AuraEnabled public String orderType {get;set;}
        @AuraEnabled public Decimal headerDiscount {get;set;}
        
        @AuraEnabled public String orderStatus {get;set;}
        @AuraEnabled public String billToAddress {get;set;}
        @AuraEnabled public String shipToAddress {get;set;}
        @AuraEnabled public String dropShipAddress {get;set;}
        @AuraEnabled public String dropShipType {get;set;}


        @AuraEnabled public String DropShipName {get;set;}
        @AuraEnabled public String DropShipAddress1 {get;set;}
        @AuraEnabled public String DropShipAddress2 {get;set;}
        @AuraEnabled public String DropShipPhone {get;set;}
        @AuraEnabled public String DropShipCountry {get;set;}
        @AuraEnabled public String DropShipCity{get;set;}
        @AuraEnabled public String DropShipZip {get;set;}
        @AuraEnabled public String DropShipState {get;set;}
        
        @AuraEnabled public List<UpdateOrderItemInfo> OrderItemList{get;set;}
    }
    public Class UpdateOrderItemInfo{
        @AuraEnabled public String orderItemId{get;set;}
        @AuraEnabled public String productDescription {get;set;}
        @AuraEnabled public String productId {get;set;}
        @AuraEnabled public String model {get;set;}
        @AuraEnabled public Decimal quantity {get;set;}
        @AuraEnabled public Date requestDate {get;set;}
        @AuraEnabled public Decimal discount {get;set;}
        @AuraEnabled public String remark {get;set;}
        
    }
    public CCM_PurchaseOrderPreview() {
        
    } 
}