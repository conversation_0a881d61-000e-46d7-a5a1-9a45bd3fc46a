/**
 * Honey
 * 2023/11/02测试类
 * 
 */
@isTest
public without sharing class CCM_CourseRegisterCtl_Test {
    @isTest
    public static void testQueryCourseSettingList() {
        // Create test data
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        objProduct.Description__c = 'courseDescription';
        insert objProduct;
        Training_Course_Setting__c testCourse = new Training_Course_Setting__c(
            Course_Name__c = objProduct.Id,
            Status__c = 'Launched'
        );
        insert testCourse;
        Course_Arrangement__c  objArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = testCourse.Id,
            Course_Date__c = Date.today().addDays(10),
            Start_Time__c = Time.newInstance(10, 0, 0, 0),
            End_Time__c = Time.newInstance(12, 0, 0, 0),
            Total_Slot__c = 500,
            Price__c = 100.00,
            Course_Start_Time__c = Date.today().addDays(-4),
            Status__c = 'Launched'
        );
        Insert objArrangement;
        Traing_Course_Product__c objCourseProduct = new Traing_Course_Product__c(
        	Course_Arrangement__c = objArrangement.Id
        );
        Insert objCourseProduct;
        // Call the method to be tested
        Test.startTest();
        List<Map<String,Object>> result = CCM_CourseRegisterCtl.queryCourseSettingList(1, 10);
        Test.stopTest();
        
        // Perform assertions
        System.assertEquals(1, result.size());

    }
    @isTest
    public static void testQueryCourseArrangementList() {
        // Create test data
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        objProduct.Description__c = 'courseDescription';
        insert objProduct;
        Training_Course_Setting__c testCourseSetting = new Training_Course_Setting__c(
            Course_Name__c = objProduct.Id
        );
        insert testCourseSetting;
        
        Course_Arrangement__c testArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = testCourseSetting.Id,
            Course_Date__c = Date.today().addDays(10),
            Start_Time__c = Time.newInstance(10, 0, 0, 0),
            End_Time__c = Time.newInstance(12, 0, 0, 0),
            Total_Slot__c = 500,
            Status__c = 'Launched',
            Price__c = 100.00
        );
        insert testArrangement;
        Product2 prod1 = new Product2();
        prod1.Name = 'BH1001';
        prod1.RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByDeveloperName().get('TLS_Product').getRecordTypeId();
        prod1.ExternalId__c = 'BH1001';
        prod1.Order_Model__c = 'BH1001';
        prod1.Master_Product__c = 'BH1001';
        insert prod1;
        Traing_Course_Product__c testProduct = new Traing_Course_Product__c(
            Course_Arrangement__c = testArrangement.Id,
            Product__c = prod1.Id
        );
        insert testProduct;
        
        Propose_Course_Arrangement__c testPropose = new Propose_Course_Arrangement__c(
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(14, 0, 0, 0),
            End_Time__c = Time.newInstance(16, 0, 0, 0),
            Description__c = 'Test Description',
            Training_Course_Setting__c = testCourseSetting.Id
           
        );
        insert testPropose;
        
        // Call the method to be tested
        Test.startTest();
        Map<String,Object> result = CCM_CourseRegisterCtl.queryCourseArrangementList(testCourseSetting.Id);
        Test.stopTest();
        
        // Perform assertions
        System.assertEquals(testCourseSetting.Id, result.get('courseId'));
    }
    @isTest
    public static void testUpsertProposeArrangement() {
        Map<String,Object> mapFeild2Value = new Map<String,Object>();
        mapFeild2Value.put('description', 'honeyTest');
        mapFeild2Value.put('proposeStartTime', '10:00:00');
        mapFeild2Value.put('proposeEndTime', '12:00:00');
        mapFeild2Value.put('proposeDate', '2023-11-11');
        String proposeId = CCM_CourseRegisterCtl.upsertProposeArrangement(JSON.serialize(mapFeild2Value));
        system.assertNotEquals(null, proposeId);

    }
    @isTest
    public static void testQueryLocationLookUpInfo() {
        // Test data setup
        Training_Location__c location1 = new Training_Location__c(Name = 'Location 1');
        Training_Location__c location2 = new Training_Location__c(Name = 'Location 2');
        insert new List<Training_Location__c>{location1, location2};
        
        Test.startTest();
        
        // Call the method
        List<Map<String, Object>> result = CCM_CourseRegisterCtl.queryLocationLookUpInfo();
        
        Test.stopTest();
        
        // Assertions
        System.assertEquals(2, result.size());
    }
    @isTest
    public static void testQueryCourseArrangementInfo() {
        // Test data setup
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            Status__c = 'Launched',
            CurrencyIsoCode = 'EUR'
        );
        insert courseArrangement;
        
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Id Sales_and_Service_Customized = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SASC).getRecordTypeId();
        Sales_Program__c salesProgram = new Sales_Program__c(
            Payment_Term__c = 'EEG01',
            Customer__c = account.Id,
            Status__c = 'Active',
            Order_Type__c = Label.SalesPrice_OrderType,
            RecordTypeId =Sales_and_Service_Customized
        );
        insert salesProgram;
        
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Country__c = 'US',
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordTypeId =CCM_Constants.BILLING_ADDRESS_RECORDTYPEID,
            StatusNew__c ='Active',
            Status__c = true
        );
        Account_Address__c shippingAddress = new Account_Address__c(
            Name = 'Shipping Address',
            Active__c = true,
            Country__c = 'DE',
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordTypeId =CCM_Constants.SHIPPING_ADDRESS_RECORDTYPEID,
            StatusNew__c ='Active',
            Status__c = true
        );
        Account_Address__c shippingAddress2 = new Account_Address__c(
            Name = 'Shipping Address',
            Active__c = true,
            Country__c = 'DE',
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordTypeId =CCM_Constants.SHIPPING_ADDRESS_RECORDTYPEID,
            StatusNew__c ='Active',
            Status__c = true
        );
        insert new List<Account_Address__c>{billingAddress, shippingAddress, shippingAddress2};
        
        Test.startTest();
        
        // Call the method
        Map<String, Object> result = CCM_CourseRegisterCtl.queryCourseArrangementInfo(courseArrangement.Id, account.Id);
        
        Test.stopTest();
        // Assertions
        System.assertEquals(account.Id, result.get('customerId'));
        System.assertEquals('EUR', result.get('currency'));
        Map<String, Object> billAddressInfo = (Map<String, Object>)result.get('BillAddressInfo');
        System.assertEquals('Sample Street', billAddressInfo.get('address'));
        
    }
    
    @isTest
    public static void testRegisterSubmit() {
        // Test data setup
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR'
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Customer__c = account.Id,
            Order_Type__c = 'Training Request',
            Status__c = 'Draft',
            CurrencyIsoCode = 'EUR',
            Training_Course__c = objSetting.Id,
            Course_Arrangement__c = courseArrangement.Id
        );
        insert courseRegister;
        String jsonApprovalInfoString = '{"recordId":"'+ courseRegister.Id+'","comments":"Sample Comments"}';
        String jsonApprovalInfoString2 = '{"recordId":"'+ courseRegister.Id+'","comments":"Sample Comments","action":"Approve"}';
        
        Test.startTest();
        
        // Call the method
        String result = CCM_CourseRegisterCtl.RegisterSubmit(jsonApprovalInfoString);
        String result2 = CCM_CourseRegisterCtl.approvalOrRejectRegister(jsonApprovalInfoString2);
        CCM_CourseRegisterCtl.queryRegisterInfo(courseRegister.Id);
       
        
        Test.stopTest();
        
        // Assertions
        System.assertEquals(CCM_Constants.SUCCESS, result);
        
        // Additional assertions for the Approval.ProcessSubmitRequest and SendNotifyNocation methods
    }
    @IsTest
    static void testuploadFileMidel(){
        
        CCM_PurchaseOrderPreview.uploadFileMidel('1231', '1.txt', '1.txt');
        //CCM_PurchaseOrderPreview.uploadFileMidel('', '', '');
       
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Country__c = 'US',
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            StatusNew__c ='Active',
            Status__c = true
        );
        insert billingAddress;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR'
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Billing_Address__c = billingAddress.Id,
            Course_Arrangement__c = courseArrangement.Id,
            Customer__c = account.Id,
            Payment_Term__c = 'EEG01',
            PCS__c = 2,
            Price_Pcs__c = 100,
            Status__c = 'Draft',
            Training_Course__c = objSetting.Id,
            CurrencyIsoCode = 'EUR',
            Trainee__c = '["Sample Trainee"]'
        );
        insert courseRegister;
        CCM_PurchaseOrderPreview.uploadFileInfo info = new CCM_PurchaseOrderPreview.uploadFileInfo();
        info.fileType = '';
        info.fileName = '1.txt';
        info.fileDate = Date.today().addDays(5);
        try{
            CCM_CourseRegisterCtl.uploadFile(courseRegister.Id, new List<CCM_PurchaseOrderPreview.uploadFileInfo>{ info });
        } catch (Exception e){
        }
        
    }
    @IsTest
    static void testuploadFile(){
        
        try{
            CCM_CourseRegisterCtl.uploadFileMidel('1231','test','Honey');
        } catch (Exception e){
        }
        
    }
    @isTest
    public static void testUpsertRegisterInfo() {
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR'
        );
        insert courseArrangement;
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Country__c = 'US',
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            StatusNew__c ='Active',
            Status__c = true
        );
        insert billingAddress;
        
        // Test data setup
        String jsonData = '{"courseArrangementId": "'+courseArrangement.Id+'", "courseRegisterId": "",'+
        ' "paymentTerm": "EEG01", "billAddressId": "'+billingAddress.Id+'", "customerId": "'+account.Id+ 
        '", "jsonInfo": "sampleJsonInfo", "pcs": "2", "trainingCourseId": "'+objSetting.Id+'"}';
        List<Map<String,String>> lstParticipants = new List<Map<String,String>>();
        Map<String,String> mapParti = new Map<String,String>();
        mapParti.put('participants', 'participant1');
        mapParti.put('SMS', 'SMS1');
        mapParti.put('email', 'email1');
        mapParti.put('remark', 'remark1');
        lstParticipants.add(mapParti);
        Map<String,String> mapParti2 = new Map<String,String>();
        mapParti2.put('participants', 'participant1');
        mapParti2.put('SMS', 'SMS1');
        mapParti2.put('email', 'email1');
        mapParti2.put('remark', 'remark1');
        lstParticipants.add(mapParti2);
       
        
        Test.startTest();
        
        // Call the method
        String result = CCM_CourseRegisterCtl.upsertRegisterInfo(jsonData, JSON.serialize(lstParticipants));
        
        Test.stopTest();
        
        // Assertions
        system.assertNotEquals(null, result);
    }
    @isTest
    public static void testUpsertRegisterCourseSetting() {
        // Test data setup
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR'
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Customer__c = account.Id,
            Course_Arrangement__c = courseArrangement.Id
        );
        insert courseRegister;
        String jsonData = '{"courseRegisterId": "'+courseRegister.Id+'", "trainingCourseId": "'+objSetting.Id+'"}';
        
        Test.startTest();
        
        // Call the method
        try{
            CCM_CourseRegisterCtl.upsertRegisterCourseSetting(jsonData);

        }catch(Exception e){
            system.debug('报错信息-->'+e.getMessage());
        }
        
        
        Test.stopTest();
        
        // Assertions
        Course_Register__c updatedCourseRegister = [SELECT Id, Training_Course__c, Status__c FROM Course_Register__c WHERE Id = : courseRegister.Id];
        System.assertEquals('Draft', updatedCourseRegister.Status__c);
    }
    @isTest
    public static void testSendEditEmailToOwner() {
        // Test data setup
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        objProduct.Description__c = 'courseDescription';
        insert objProduct;
        Training_Course_Setting__c testCourseSetting = new Training_Course_Setting__c(
            Course_Name__c = objProduct.Id
        );
        insert testCourseSetting;
        
        Course_Arrangement__c testArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = testCourseSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(10, 0, 0, 0),
            End_Time__c = Time.newInstance(12, 0, 0, 0),
            Total_Slot__c = 500,
            Status__c = 'Launched',
            Price__c = 100.00
        );
        insert testArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Status__c = 'submitted',
            Customer__c = account.Id,
            //CreatedById = account.Id,
            Course_Arrangement__c = testArrangement.Id
        );
        insert courseRegister;
  
        
        Test.startTest();
        
        // Call the method
        try{
            CCM_CourseRegisterCtl.sendEditEmailToOwner(courseRegister.Id);

        }catch(Exception e){
            system.debug('报错信息-->'+e.getMessage());
        }
        
        
        Test.stopTest();
        
        // Assertions
        // Add assertions if needed
    }@isTest
    public static void testUpsertRegisterCourseArrangement() {
        // Test data setup
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR'
        );
        insert courseArrangement;
        Account account = new Account(Name = 'Sample Account');
        insert account;
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Country__c = 'US',
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            StatusNew__c ='Active',
            Status__c = true
        );
        insert billingAddress;
        Course_Register__c courseRegister = new Course_Register__c(
            Status__c = 'Draft',
            Customer__c = account.Id,
            Course_Arrangement__c = courseArrangement.Id
        );
        insert courseRegister;
        String jsonData = '{"courseArrangementId": "'+courseArrangement.Id +'", "courseRegisterId": "'+courseRegister.Id+'", "pcs": "2", '+
        '"trainingCourseId": "'+objSetting.Id+'"}';
        
        
        
        Test.startTest();
        
        // Call the method
        String result = CCM_CourseRegisterCtl.upsertRegisterCourseArrangement(jsonData);
        CCM_CourseRegisterCtl.ReceiptCourse(courseRegister.Id);
        CCM_CourseRegisterCtl.GetCurrentUserCustomer();
        
        Test.stopTest();
        
        // Assertions
        System.assertEquals(null, result);
        
    }
    @isTest
    public static void testQueryBillAddressInfo() {
        // Test data setup
        Account account = new Account(Name = 'Sample Account');
        insert account;
        String addressName = 'Address';
        String customerId = account.Id;
        
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordTypeId = CCM_Constants.BILLING_ADDRESS_RECORDTYPEID,
            StatusNew__c ='Active',
            Status__c = true
        );
        insert billingAddress;
        Account_Address__c shippingAddress = new Account_Address__c(
            Name = 'Shipping Address',
            Active__c = true,
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordTypeId = CCM_Constants.SHIPPING_ADDRESS_RECORDTYPEID,
            StatusNew__c ='Active',
            Status__c = true
        );
        insert shippingAddress;
        
        Contact con = new Contact(LastName ='Test Contact');
        insert con;
        Related_Dealer_Contact__c RDC_c = new Related_Dealer_Contact__c(Address__c = billingAddress.Id, Contact__c=con.Id);
        
        Test.startTest();
        
        // Call the method
        List<Map<String, String>> result = CCM_CourseRegisterCtl.queryBillAddressInfo(addressName, customerId);
        List<Map<String, String>> result2 = CCM_CourseRegisterCtl.queryBillAddressInfo('', customerId);
                
        Test.stopTest();
        
        Account_Address__c addre = new Account_Address__c(
            Name = 'Test Address',
            Active__c = true,
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordTypeId = CCM_Constants.SHIPPING_ADDRESS_RECORDTYPEID,
            StatusNew__c ='Active',
            Status__c = true
        );
        insert addre;
        Account acc = new Account(Name = 'Sample Account');
        insert acc;
        Contact con2 = new Contact(LastName ='Test Contact');
        insert con2;
        Related_Dealer_Contact__c RDC_c2 = new Related_Dealer_Contact__c(Address__c = addre.Id, Contact__c=con2.Id);
        CCM_CourseRegisterCtl.queryBillAddressInfo('Test', acc.Id);
    }
    @isTest
    public static void testQueryShipAddressInfo() {
        // Test data setup
        Account account = new Account(Name = 'Sample Account');
        insert account;
        String addressName = 'Sample Address';
        String customerId = account.Id;
        
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Ship Address',
            Active__c = true,
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordTypeId = CCM_Constants.SHIPPING_ADDRESS_RECORDTYPEID,
            StatusNew__c ='Active',
            Status__c = true
        );
        insert billingAddress;
        
        Test.startTest();
        
        // Call the method
        List<Map<String, String>> result = CCM_CourseRegisterCtl.queryShipAddressInfo(addressName, customerId);
        List<Map<String, String>> result2 = CCM_CourseRegisterCtl.queryShipAddressInfo('', customerId);
        
        Test.stopTest();
        
        
    }
    @isTest
    public static void testQueryAvaliableCustomer() {
        // Test data setup
        String queryName = 'Sample';
        
        Account account = new Account(
            Name = 'Sample Account',
            Company__c = 'Sample Company',
            AccountNumber = 'Sample Account Number',
            CurrencyIsoCode = 'EUR',
            RecordType = [SELECT Id, DeveloperName FROM RecordType WHERE DeveloperName = 'Association_Group']
        );
        insert account;
        
        Test.startTest();
        
        // Call the method
        List<Map<String, String>> result = CCM_CourseRegisterCtl.queryAvaliableCustomer(queryName);
        
        Test.stopTest();
        
        // Assertions
        System.assertEquals(1, result.size());
    }
    @isTest
    public static void testQueryAddressByCustomer() {
        // Test data setup
        Account account = new Account(
            Name = 'Sample Account',
            Company__c = 'Sample Company',
            AccountNumber = 'Sample Account Number',
            CurrencyIsoCode = 'EUR',
            RecordType = [SELECT Id, DeveloperName FROM RecordType WHERE DeveloperName = 'Association_Group']
        );
        insert account;
        String customerId = account.Id;
        Id Sales_and_Service_Customized = Schema.SObjectType.Sales_Program__c.getRecordTypeInfosByDeveloperName().get(CCM_Constants.AUTHORIZED_BRAND_RECORDTYPE_DEVELOPERNAME_SASC).getRecordTypeId();
        Sales_Program__c salesProgram = new Sales_Program__c(
            Payment_Term__c = 'EEG01',
            Customer__c = account.Id,
            Status__c = 'Active',
            RecordTypeId = Sales_and_Service_Customized
        );
        insert salesProgram;
        
        Account_Address__c billingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordTypeId = CCM_Constants.BILLING_ADDRESS_RECORDTYPEID,
            StatusNew__c ='Active',
            Status__c = true
        );
        insert billingAddress;
        Account_Address__c shippingAddress = new Account_Address__c(
            Name = 'Billing Address',
            Active__c = true,
            Customer__c = account.Id,
            City__c = 'Sample City',
            Street_1__c = 'Sample Street',
            Postal_Code__c = '12345',
            RecordTypeId = CCM_Constants.SHIPPING_ADDRESS_RECORDTYPEID,
            StatusNew__c ='Active',
            Status__c = true
        );
        insert shippingAddress;
        
        Test.startTest();
        
        // Call the method
        Map<String, Object> result = CCM_CourseRegisterCtl.queryAddressByCustomer(customerId);
        
        Test.stopTest();
        
       
        System.assertEquals(salesProgram.Id, result.get('authBrandId'));
        
       
    }
    
	// @isTest
    // public static void testJudgeCustomerCountry(){
    //     Account account1 = new Account(
    //         Name = 'Sample Account1',
    //         Company__c = 'Sample Company1',
    //         AccountNumber = 'Sample Account Number1',
    //         CurrencyIsoCode = 'EUR',
    //         //ASSOCIATION_GROP
    //         RecordType = [SELECT Id, DeveloperName FROM RecordType WHERE DeveloperName = 'Association_Group'],
    //         Country__c = 'DE'
    //     );
        
    //     Account account2 = new Account(
    //         Name = 'Sample Account2',
    //         Company__c = 'Sample Company2',
    //         AccountNumber = 'Sample Account Number2',
    //         CurrencyIsoCode = 'EUR',
    //         RecordType = [SELECT Id, DeveloperName FROM RecordType WHERE DeveloperName = 'Association_Group'],
    //         Country__c = 'AD'
    //     );
        
    //     insert account1;
    //     insert account2;
        
    //     Test.startTest();
        
    //     // Call the method
    //     Boolean account1Result = CCM_CourseRegisterCtl.judgeCustomerCountry();
    //     Boolean account2Result = CCM_CourseRegisterCtl.judgeCustomerCountry();
        
    //     Test.stopTest();
        
    //     //System.assertEquals(true, account1Result);
    //     //System.assertEquals(false,account2Result);
    // }
    
    public CCM_CourseRegisterCtl_Test() {

    }
    
    @IsTest
    static void testQueryOrderInfo(){
        CCM_CourseRegisterCtl.queryOrderInfo('');
    }
   
}