/**
 * Author : Honey
 * Description:查询Overiew List
 * Date: 2023/07/15
 *  */
public without sharing class CCM_SalesOverViewListCtl {
    String userId = UserInfo.getUserId();
    public static String RecordType = 'Sales_Rep_Stock'; 
    public static String sqlOrderString = 'SELECT Id,RecordType.DeveloperName,Name,Request_No__c,Model__c,Product_Description__c,Application_No__c,Request_For__c,'
    +'Product_Description__r.Name,Product_Description__r.Item_Description_EN__c,Product_Type__c,SerialNumber__c,Sales_Rep_Stock_Header__c,Sales_Rep_Stock_Header__r.Name,'
    +'Usage_Type__c,Sales_Rep_Stock_Lentout__c,Sales_Rep_Stock_Lentout__r.Customer__c,Sales_Rep_Stock_Lentout__r.Prospect__c,Product_Description__r.RecordType_Name__c,'
    +'Sales_Rep_Stock_Lentout__r.Customer__r.Name,Sales_Rep_Stock_Lentout__r.Prospect__r.Name,Sales_Rep_Stock_Header__r.Stock_Request__c,Sales_Rep_Stock_Header__r.Stock_Request__r.Name,'
    +'Sales_RepStock_Lent_Out_Item__r.Usage_Start_Date__c,Sales_RepStock_Lent_Out_Item__r.Usage_End_Date__c,Sales_RepStock_Lent_Out_Item__c, '
    +'Product_Status_Rating__c, '
    +'(SELECT SerialNumber__c, Usage_Start_Date__c, Usage_End_Date__c, Sales_Rep_Stock_Lentout__r.Customer__r.Name, Sales_Rep_Stock_Lentout__r.Prospect__r.Name, Sales_Rep_Stock_Lentout__r.Lent_Out_Number__c FROM Sales_RepStock_Lent_Out_Items__r)'
    +' FROM Sales_Rep_Stock__c WHERE isDelete__c = false AND RecordType.DeveloperName = \'' + RecordType+ '\'';
    public static String userType = '';
    public static Integer AllQty = 0;
    //查询salesRepStock详细信息
    @AuraEnabled
    public static String getOverViewList(Integer pageNumber, Integer pageSize, String fifter){
        try{
            system.debug('进入前--->0'+sqlOrderString);
            User usr = Util.getUserInfo(UserInfo.getUserId());
            if(usr.Profile.Name != Label.ADMIN){
                sqlOrderString += ' AND Request_For__c =\'' + usr.Id + '\'';
            }
            InitData initD = new InitData();
                List<DataTableWrapperProduct> wrappers = new List<DataTableWrapperProduct>();
                //Order
                system.debug('brfore filter --->'+sqlOrderString);
                String filterCondition2 = getFilterCondition(fifter);
                sqlOrderString += filterCondition2;
                system.debug('filterCondition2--->'+filterCondition2);
                sqlOrderString += ' ORDER BY CreatedDate Desc LIMIT 49999 ';
                system.debug('sqlOrderString--->'+sqlOrderString);
                List<Sales_Rep_Stock__c> allSalesRepList = Database.query(sqlOrderString);
                //获取LentOutID用于查询Aggrement信息
                List<String> lstLentOutIds = new List<String>();
                Map<String,List<Sales_Rep_Stock__c>> mapProductId2SalesRep = new Map<String,List<Sales_Rep_Stock__c>>();
                if (allSalesRepList != null && allSalesRepList.size() > 0){
                    //先将相同的Product放在一起
                    for(Sales_Rep_Stock__c objSalesRep : allSalesRepList){
                        List<Sales_Rep_Stock__c> lstSalesByProduct = mapProductId2SalesRep.containsKey(objSalesRep.Model__c)?
                        mapProductId2SalesRep.get(objSalesRep.Model__c) : new List<Sales_Rep_Stock__c>();
                        lstSalesByProduct.add(objSalesRep);
                        mapProductId2SalesRep.put(objSalesRep.Model__c,lstSalesByProduct);
                        lstLentOutIds.add(objSalesRep.Sales_Rep_Stock_Lentout__c);
                    }
                    //调用查询文件方法
                    Map<String,Object> mapObjectId2Attachments  = CCM_SalesRepStock_LentOut.getMediaResourceByRecordList(lstLentOutIds);
                    //对同一个产品遍历
                    for(String Model : mapProductId2SalesRep.keySet()){
                        List<Sales_Rep_Stock__c> lstSalesByProduct = mapProductId2SalesRep.get(Model);
                        DataTableWrapperProduct objWrapper = new DataTableWrapperProduct();
                        List<DataTableWrapperSN> lstWrapperSn = new List<DataTableWrapperSN>();
                        //创建lentoutId的List
                       
                        if(lstSalesByProduct != null && lstSalesByProduct.size()>0){
                            for(Sales_Rep_Stock__c objSalesRep : lstSalesByProduct){
                                 objWrapper.modelNumber = objSalesRep.Model__c;
                                objWrapper.productDescription = objSalesRep.Product_Description__r.Item_Description_EN__c;
                                objWrapper.productType = objSalesRep.Product_Description__r.RecordType_Name__c;

                                DataTableWrapperSN objWrapperSN = new DataTableWrapperSN();
                                objWrapperSN.id = objSalesRep.Id;
                                AllQty = AllQty +1 ;
                                objWrapperSN.snno = objSalesRep.SerialNumber__c;
                                objWrapperSN.usageType = objSalesRep.Usage_Type__c;
                                objWrapperSN.requestNumber = objSalesRep.Sales_Rep_Stock_Header__r.Stock_Request__r.Name;
                                objWrapperSN.lentOutToCustomer = objSalesRep.Sales_Rep_Stock_Lentout__r.Customer__r.Name;
                                objWrapperSN.lentOutToProspect = objSalesRep.Sales_Rep_Stock_Lentout__r.Prospect__r.Name;
                                objWrapperSN.usageStartDate = objSalesRep.Sales_RepStock_Lent_Out_Item__r.Usage_Start_Date__c;
                                objWrapperSN.usageEndDate = objSalesRep.Sales_RepStock_Lent_Out_Item__r.Usage_End_Date__c;
                                if(objSalesRep.Sales_RepStock_Lent_Out_Items__r != null && !objSalesRep.Sales_RepStock_Lent_Out_Items__r.isEmpty()) {
                                    objWrapperSN.lentOutToCustomer = objSalesRep.Sales_RepStock_Lent_Out_Items__r[0].Sales_Rep_Stock_Lentout__r.Customer__r.Name;
                                    objWrapperSN.lentOutToProspect = objSalesRep.Sales_RepStock_Lent_Out_Items__r[0].Sales_Rep_Stock_Lentout__r.Prospect__r.Name;
                                    objWrapperSN.snno = objSalesRep.Sales_RepStock_Lent_Out_Items__r[0].SerialNumber__c;
                                    objWrapperSN.usageStartDate = objSalesRep.Sales_RepStock_Lent_Out_Items__r[0].Usage_Start_Date__c;
                                    objWrapperSN.usageEndDate = objSalesRep.Sales_RepStock_Lent_Out_Items__r[0].Usage_End_Date__c;
                                    objWrapperSN.requestNumber = objSalesRep.Sales_RepStock_Lent_Out_Items__r[0].Sales_Rep_Stock_Lentout__r.Lent_Out_Number__c;
                                }
                                Map<String,Object> mapFeild2Value = (Map<String,Object>)mapObjectId2Attachments.get( objSalesRep.Sales_Rep_Stock_Lentout__c);
                                if(mapFeild2Value != null){
                                    //获取使用前文件信息
                                    List<Object> lstmapMediabefore = (List<Object>)mapFeild2Value.get('beforeusageimage');
                                    if( lstmapMediabefore != null){
                                        List<ImageUsageInfo> lstImageBefore = new List<ImageUsageInfo>();
                                        for(Object  objInfo : lstmapMediabefore){
                                            Map<String,Object>  mapMedia = ( Map<String,Object> ) objInfo;
                                            ImageUsageInfo objImage = new ImageUsageInfo();
                                            objImage.lentOUtNumber = (String)mapMedia.get('no');
                                            objImage.Image =  (String)mapMedia.get('documentid');
                                            objImage.ImageName =  (String)mapMedia.get('name');
                                            objImage.ImageUrl =  (String)mapMedia.get('url');
                                            lstImageBefore.add(objImage);
                                        }
                                        objWrapperSN.imageBeforeUse = lstImageBefore;

                                    }
                                    

                                    //获取使用后文件信息
                                    List<Object> lstmapMediaAfter =  (List<Object>)mapFeild2Value.get('afterusageimage');
                                    if(lstmapMediaAfter != null){
                                    
                                        List<ImageUsageInfo> lstImageAfter = new List<ImageUsageInfo>();
                                        for(Object  objInfo : lstmapMediaAfter){
                                            Map<String,Object>  mapMedia = ( Map<String,Object> ) objInfo;
                                            ImageUsageInfo objImage = new ImageUsageInfo();
                                            objImage.lentOUtNumber =  (String)mapMedia.get('no');
                                            objImage.Image =  (String)mapMedia.get('documentid');
                                            objImage.ImageName =  (String)mapMedia.get('name');
                                            objImage.ImageUrl =  (String)mapMedia.get('url');
                                            lstImageAfter.add(objImage);
                                        }
                                        objWrapperSN.imageAfterUse = lstImageAfter;

                                    }
                                    //获取上传的Aggrement
                                    List<Object> lstmapMediaAggrement = (List<Object>)mapFeild2Value.get('manualagreement');
                                    List<ImageUsageInfo> lstImageAggrement = new List<ImageUsageInfo>();
                                    if(lstmapMediaAggrement != null){
                                        for(Object  objInfo : lstmapMediaAggrement){
                                            Map<String,Object>  mapMedia = ( Map<String,Object> ) objInfo;
                                            ImageUsageInfo objImage = new ImageUsageInfo();
                                            objImage.lentOUtNumber =  (String)mapMedia.get('no');
                                            objImage.Image =  (String)mapMedia.get('documentid');
                                            objImage.ImageName =  (String)mapMedia.get('name');
                                            objImage.ImageUrl =  (String)mapMedia.get('url');
                                            lstImageAggrement.add(objImage);
                                        }
                        
                        
                                        objWrapperSN.agreement = lstImageAggrement.size() > 0  ?  lstImageAggrement[0] : null;

                                    }
                                    
                                    //获取otherAttachment

                                    List<Object> lstmapMediaOther = (List<Object>)mapFeild2Value.get('otherattachment');
                                    List<ImageUsageInfo> lstImageOther = new List<ImageUsageInfo>();
                                    if(lstmapMediaOther != null){
                                        for(Object  objInfo : lstmapMediaOther){
                                            Map<String,Object>  mapMedia = ( Map<String,Object> ) objInfo;
                                            ImageUsageInfo objImage = new ImageUsageInfo();
                                            objImage.lentOUtNumber =  (String)mapMedia.get('no');
                                            objImage.Image =  (String)mapMedia.get('documentid');
                                            objImage.ImageName =  (String)mapMedia.get('name');
                                            objImage.ImageUrl =  (String)mapMedia.get('url');
                                            lstImageOther.add(objImage);
                                        }
                        
                        
                                        objWrapperSN.otherAttachment = lstImageOther;

                                    }
                                }
                                
                                objWrapperSN.productStatusRating = objSalesRep.Product_Status_Rating__c;
                               
                                
                                lstWrapperSn.add(objWrapperSN);
                            }
                        }
                        objWrapper.SNInfoList = lstWrapperSn;
                                                objWrapper.qty = lstWrapperSn.size();
                        wrappers.add(objWrapper);
                    }
                                    }
                if (wrappers != null && wrappers.size() > 0){
                    /*initD.allData = wrappers;*/
                    initD.currentUserName = UserInfo.getName();
                    initD.totalRecords = wrappers.size();
                    initD.AllSnRecords = AllQty;
                    initD.currentData = getCurrentData(wrappers, pageNumber, pageSize);
                }
            
                return JSON.serialize(initD);
            
        }catch(Exception e){
                throw new AuraHandledException(e.getMessage());
        }
        
        
    }
    //查询User的DropShip信息
    @AuraEnabled
    public static Map<String,Object> queryDropShipAddress(String customerId){
        system.debug('接收到的参数--->'+customerId);
        List<AddressInfo> lstAddressInfo = new List<AddressInfo>();
        Map<String,Object> mapFeild2ValueReturn = new Map<String,Object>();
        try {
            //通过CustomerId查询DropShip信息
            List<Account_Address__c> lstAccountAddress = [
                SELECT Id,Customer__c,RecordType_Name__c,Final_Address__c,Prospect__c,
                Company_name_1__c,Country__c,City__c,Address1__c,Postal_Code__c
                FROM Account_Address__c WHERE Customer__c = :customerId AND  RecordType_Name__c = 'Dropship_Shipping_Address'
                AND Status__c = true
            ];
            for(Account_Address__c objAccountAddress : lstAccountAddress){
                AddressInfo objAddress = new AddressInfo();
                objAddress.id = objAccountAddress.Id;
                objAddress.companyName = objAccountAddress.Company_name_1__c;
                objAddress.country = objAccountAddress.Country__c;
                objAddress.city = objAccountAddress.City__c;
                objAddress.address = objAccountAddress.Address1__c;
                objAddress.postalCode = objAccountAddress.Postal_Code__c;
                lstAddressInfo.add(objAddress);
            }
            mapFeild2ValueReturn.put('state', CCM_Constants.SUCCESS);
            mapFeild2ValueReturn.put('data', JSON.serialize(lstAddressInfo));


            
        } catch (Exception e) {
            mapFeild2ValueReturn.put('state', CCM_Constants.ERROR);
            throw new AuraHandledException(e.getMessage());
        }
        return mapFeild2ValueReturn;
    }
    public class AddressInfo{
        public String id {get;set;}
        public String companyName {get;set;}
        public String country {get;set;}
        public String city {get;set;}
        public String address {get;set;}
        public String postalCode {get;set;}
    }
    
    public CCM_SalesOverViewListCtl() {

    }
    public static String getFilterCondition(String filterString){
        String condicationStr = '';
        if (String.isNotBlank(filterString)){
            FilterWrapper filters = (FilterWrapper)JSON.deserialize(filterString, FilterWrapper.class);
           
            if (String.isNotBlank(filters.snno)) {
                system.debug('SerialNumber__c 筛选');
                condicationStr += ' AND SerialNumber__c LIKE \'%' + filters.snno.trim() + '%\'';
            }
            if (String.isNotBlank(filters.modelNumber)) {
                system.debug('modelNumber 筛选');
                condicationStr += ' AND Model__c LIKE \'%' + filters.modelNumber.trim() + '%\'';
            }
            if (String.isNotBlank(filters.requestNumber)) {
                system.debug('requestNumber筛选');
                condicationStr += ' AND Sales_Rep_Stock_Header__r.Stock_Request__r.Name like \'%' + filters.requestNumber + '%\'';
            }
            if (String.isNotBlank(filters.lentOutToCustomer)) {
                //String minDate = filters.submitDateMin + 'T23:59:00Z';
                system.debug('添加customer筛选');
                condicationStr += ' AND Sales_Rep_Stock_Lentout__r.Customer__c =\'' + (String) filters.lentOutToCustomer.trim() + '\'';
            }
            if (String.isNotBlank(filters.lentOutToProspect)) {
                system.debug('lentOutToProspect 筛选');
                //String maxDate = filters.submitDateMax + 'T23:59:00Z';
                condicationStr += ' AND Sales_Rep_Stock_Lentout__r.Prospect__c =\'' + (String) filters.lentOutToProspect.trim() + '\'';
            }
        }
        return condicationStr;
    }

    //分页
    public static List<DataTableWrapperProduct> getCurrentData(List<DataTableWrapperProduct> allData,  Decimal pageNumber, Integer pageSize) {
        List<DataTableWrapperProduct> currentData = new List<DataTableWrapperProduct>();
        Integer min = ((Integer)pageNumber - 1) * pageSize;
        Integer max = (Integer)pageNumber * pageSize -1;
        for (Integer i = min ; i <= max; i++ ) {
            if (i < allData.size()) {
                currentData.add(allData.get(i));
            }
        }
        return currentData;
    }

    //过滤条件
    public class FilterWrapper{
        public String snno {get; set;}
        public String modelNumber {get; set;}
        public String requestNumber {get; set;}
        public String lentOutToCustomer {get; set;}
        public String lentOutToProspect {get; set;}
    }
    public class DataTableWrapperProduct{
        public String modelNumber {get; set;}
        public String productDescription {get; set;}
        public Decimal qty {get; set;}
        public String productType {get; set;}
        public List<DataTableWrapperSN> SNInfoList {get;set;}
    }
    public class DataTableWrapperSN{
        public String id {get; set;}
        public String snno {get; set;}
        public String usageType {get; set;}
        public String requestNumber {get; set;}
        public String lentOutToCustomer {get; set;}
        public String lentOutToProspect {get; set;}
        public Date usageStartDate {get; set;}
        public Date usageEndDate {get; set;}
        public ImageUsageInfo agreement {get; set;}
        public List<ImageUsageInfo > imageBeforeUse {get; set;}
        public List<ImageUsageInfo > imageAfterUse {get; set;}
        public List<ImageUsageInfo > otherAttachment {get; set;}
        public String productStatusRating {get; set;}
        
    }
    public class ImageUsageInfo{
        public String ImageName;
        public String lentOUtNumber;
        public String Aggrement;
        public String Image;
        public String ImageUrl;
    }
    //最终返回的结果
    public class InitData{
        public List<DataTableWrapperProduct> currentData;
        
        public Integer totalRecords;
        public Integer AllSnRecords;
        public String  currentUserName;
        
        public Boolean isSuccess;
        
        public String errorMessage;
        
        public InitData(){
            this.currentData = new List<DataTableWrapperProduct>();
            this.totalRecords = 0;
            this.isSuccess = true;
            this.errorMessage = '';
            this.currentUserName = '';
        }
        
    }
   

}