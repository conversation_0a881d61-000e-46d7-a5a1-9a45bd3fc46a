<!--
 - Created by gluo006 on 7/24/2019.
 -->

 <aura:component 
    description="CCM_Community_Service" 
    implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes"
    controller="WarrantyClaimCreateController"
    access="global"
>
    <!-- add by haibo   -->
    <aura:attribute name="project" type="String"/>
    <aura:attribute name="projectList" type="List"/>
    <aura:attribute name="isBusy" type="Boolean" default="false"/>
    <aura:attribute name="currencySymbol" type="String"/>
    <aura:attribute name="claimType" type="String"/>
    <aura:attribute name="brandOptions" type="List" default="[]"/>
    <aura:attribute name="consumerTypeOptions" type="List" default="[]"/>
    <aura:attribute name="isPortal" type="Boolean" default="false"/>
    <aura:attribute name="receiptName" type="String" default=""/>
    <aura:attribute name="receiptLink" type="String" default=""/>
    <aura:attribute name="placeOfPurchase" type="String" default=""/>
    <aura:attribute name="purchaseDate" type="String" default=""/>
    <aura:attribute name="expiredDate" type="String" default=""/>
    <aura:attribute name="accId" type="String" default=""/>
    <aura:attribute name="warrantyId" type="String" default=""/>
    <aura:attribute name="permission" type="String" default=""/>
    <aura:attribute name="ownerPermission" type="Boolean" default="false"/>
    <aura:attribute name="viewPermission" type="Boolean" default="false"/>
    <aura:attribute name="noPermission" type="Boolean" default="false"/>
    <aura:attribute name="noWarranty" type="Boolean" default="true"/>
    <aura:attribute name="distributorOrDealer" type="String" default=""/>
    <aura:attribute name="receiptUploadInfo" type="Map" default="{}"/>
    <aura:attribute name="overTimeDescription" type="String" default=""/>
    <aura:attribute name="claimId" type="String" default=""/>
    <aura:attribute name="claimStatus" type="String" default=""/>
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="emailValid" type="Boolean" default="false"/>
    <aura:attribute name="warrantyStatus" type="String" default=""/>
    <aura:attribute name="activeSections" type="List" default="['A']" />
    <aura:attribute name="isCopy" type="String" default="" />
    <aura:attribute name="showNewPOButton" type="Boolean" default="false" />

    <!-- model info -->
    <aura:attribute name="unitPrice" type="String" default=""/>
    <aura:attribute name="modelProductInfo" type="Map" default="{}"/>
    <aura:attribute name="partProductInfo" type="Map" default="{}"/>


    <!-- base info -->
    <aura:attribute name="consumerType" type="String" default=""/>
    <aura:attribute name="emailAddress" type="String" default=""/>
    <aura:attribute name="dropOffDate" type="String" default=""/>
    <aura:attribute name="repairDate" type="String" default=""/>
    <aura:attribute name="brand" type="String" default="EGO"/>
    <aura:attribute name="serialNumber" type="String" default=""/>
    <aura:attribute name="modelNumberInfo" type="Map" default="{}"/>
    <aura:attribute name="modelNumber" type="String" default=""/>
    <aura:attribute name="productId" type="String" default=""/>
    <aura:attribute name="productName" type="String" default=""/>
    <aura:attribute name="consumerId" type="String" default=""/>
    <aura:attribute name="customerClaimReferenceNumber" type="String" />
    
    <!-- Service Information -->
    <aura:attribute name="serviceType" type="String" default="Repair"/>
    <aura:attribute name="serviceOption" type="List" default="[]"/>
    <aura:attribute name="replacementType" type="String" default=""/>
    <aura:attribute name="replacementOption" type="List" default="[]"/>
    <aura:attribute name="replacementByDistributor" type="List" default="[]"/>
    <aura:attribute name="replacementByDealer" type="List" default="[]"/>
    <aura:attribute name="repairType" type="String" default="Parts"/>
    <aura:attribute name="repairOption" type="List" default="[]"/>
    <aura:attribute name="repairByDistributor" type="List" default="[]"/>
    <aura:attribute name="repairByDealer" type="List" default="[]"/>
    <aura:attribute name="scenarioType" type="String" default="Scenario 3"/>
    <aura:attribute name="addressInfo" type="Map" default="{}"/>
    <aura:attribute name="description" type="String" default=""/>
    <aura:attribute name="isAddPartsDisabled" type="Boolean" default="false"/>
    <aura:attribute name="billAddressInfo" type="Map" default="{}"/>
    <aura:attribute name="failureCode" type="String" default="" />
    <aura:attribute name="failureCode4" type="String" default="" />
    <aura:attribute name="failureCodeOptions" type="List" default="[]" />
    <aura:attribute name="repairableParts" type="String" default=""/>
    <aura:attribute name="repairablePartsInfo" type="Map" default="{}"/>
    <aura:attribute name="projectCode" type="String" default=""/>
    <aura:attribute name="projectId" type="String" default=""/>
    <aura:attribute name="isOpen" type="Boolean" default="false" />
    <aura:attribute name="isNeedProject" type="Boolean" default="false" />
    <aura:attribute name="isSelected" type="Boolean" default="false" />
    <aura:attribute name="selectedProject" type="String" default="" />
    <aura:attribute name="newSerialNumber" type="String" default="" />
    <aura:attribute name="showNewSN" type="Boolean" default="false" />
    <aura:attribute name="showNewSNFlag" type="Boolean" default="false" />
    <!-- parts table -->
    <aura:attribute name="partsItemList" type="List" default=""/>

    <!-- 价格计算 -->
    <aura:attribute name="laborRate" type="Decimal" default=""/>
    <aura:attribute name="laborPriceBook" type="String" />
    <aura:attribute name="laborTime" type="Decimal" default="0"/>
    <aura:attribute name="overTimeHour" type="Decimal" default="0"/>
    <aura:attribute name="partsCost" type="Decimal" default="0"/>
    <aura:attribute name="totalPrice" type="Decimal" default="0"/>
    <aura:attribute name="finialLaborHour" type="Decimal" default="0"/>
    <aura:attribute name="laborCostSubtotal" type="Decimal" default="0"/>

    <aura:attribute name="rejectComments" type="String" />

    <aura:attribute name="paymentStatus" type="String" />

    <aura:attribute name="isBatteryDiagnosticFee" type="Boolean" default="false" />
    <aura:attribute name="diagnosticFee" type="Decimal" default="0" />
    <aura:attribute name="batteryDiagnosticFee" type="Decimal" default="15" />
    <aura:attribute name="batteryDiagnosticScope" type="Boolean" default="false" />

    
    <!-- handler -->
    <aura:handler name="change" value="{!v.serviceType}" action="{!c.changeServiceType}"/>
    <aura:handler name="change" value="{!v.replacementType}" action="{!c.changeReplacementType}"/>
    <aura:handler name="change" value="{!v.repairType}" action="{!c.changeRepairType}"/>
    <aura:handler name="change" value="{!v.laborRate}" action="{!c.getFinialLaborHour}"/>
    <aura:handler name="change" value="{!v.laborTime}" action="{!c.getFinialLaborHour}"/>
    <aura:handler name="change" value="{!v.overTimeHour}" action="{!c.getFinialLaborHour}"/>
    <aura:handler name="compEvent" event="c:CCM_CommonEvent" action="{!c.handlePartsSelect}"/>


    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <div class="slds-grid slds-grid_align-space">
        <lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed loading-wrap' : 'slds-hide' }"/>
        <div class="{!(v.isPortal ? 'portal-wrap' : 'crm-wrap')}">
            <section class="slds-p-around_x-small">
                <article class="slds-card">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate" title="{!$Label.c.CCM_BasicInformation}">
                                        <span><strong>{!$Label.c.CCM_BasicInformation}</strong></span>
                                    </span>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="baseInfo-wrap">
                            <!-- baseinfo -->
                            <div class="customerInfo">
                                <div class="slds-grid slds-wrap secondLine">
                                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                        <label class="slds-form-element__label">{!$Label.c.CCM_ClaimStatus}:</label>
                                        {!v.claimStatus || $Label.c.CCM_Draft}
                                    </div>
                                    <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium">
                                        <label class="slds-form-element__label">{!$Label.c.CCM_PaymentStatus}:</label>
                                        {!v.paymentStatus || $Label.c.CCM_NA}
                                    </div>
                                </div>
                                <!-- basic info 查询条件 -->
                                <div class="search-wrap">
                                    <lightning:layout horizontalAlign="space" class="margin-size">
                                        <!-- Consumer Type -->
                                        <lightning:layoutItem alignmentBump="right" size="2">
                                            <lightning:combobox
                                                aura:id="consumerType"
                                                value="{!v.consumerType}"
                                                options="{!v.consumerTypeOptions}"
                                                label="{!$Label.c.CCM_CustomerType}"
                                                placeholder=""
                                            />
                                        </lightning:layoutItem>
                                        <!-- Email Address -->
                                        <lightning:layoutItem alignmentBump="right" size="2">
                                            <lightning:input aura:id="emailAddress" type="Email" label="{!$Label.c.CCM_EmailAddress}" value="{!v.emailAddress}" onblur="{!c.changeEmailAddress}"/>
                                        </lightning:layoutItem>
                                        <!-- Drop-off Date -->
                                        <lightning:layoutItem alignmentBump="right" size="2" class="required-wrap">
                                            <lightning:input class="field-required" aura:id="dropOffDate" label="{!$Label.c.CCM_DropOffDate}" type="date" value="{!v.dropOffDate}" onblur="{!c.changeDropOffDate}"/>
                                            <div aura:id="dropOffDate-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                        </lightning:layoutItem>
                                        <!-- Repair Date -->
                                        <lightning:layoutItem alignmentBump="right" size="2" class="required-wrap">
                                            <lightning:input class="field-required" aura:id="repairDate" label="{!$Label.c.CCM_RepairDate}" type="date" value="{!v.repairDate}" onblur="{!c.changeRepairDate}"/>
                                            <div aura:id="repairDate-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                        </lightning:layoutItem>
                                    </lightning:layout>
                                    <lightning:layout horizontalAlign="space">
                                        <!-- Brand -->
                                        <lightning:layoutItem alignmentBump="right" size="2" class="required-wrap">
                                            <lightning:combobox
                                                aura:id="brand"
                                                value="{!v.brand}"
                                                options="{!v.brandOptions}"
                                                label="{!$Label.c.CCM_Brand}"
                                                class="field-required"
                                                onblur="{!c.changeBrand}"
                                                placeholder=""
                                            />
                                            <div aura:id="brand-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                        </lightning:layoutItem>
                                        <!-- Serial Number -->
                                        <lightning:layoutItem alignmentBump="right" size="2">
                                            <lightning:input label="{!$Label.c.CCM_SerialNumber}" type="text" value="{!v.serialNumber}" minlength="15" maxlength="15" onblur="{!c.changeSerialNumber}" messageWhenTooLong="{!$Label.c.CCM_Enter15SerialNumberError}"/>
                                        </lightning:layoutItem>
                                        <!-- Model Number -->
                                        <lightning:layoutItem alignmentBump="right" size="2" class="required-wrap">
                                            <c:CCM_Community_LookUp 
                                                fieldName="Model Number"
                                                fieldNameLabel="{!$Label.c.CCM_ModelNumber}"
                                                selectedValue="{!v.modelNumberInfo}"
                                                aura:id="modelNumber"
                                                class="field-required"
                                                isClaim="true"
                                                accId="{!v.accId}"
                                                onSelect="{!c.selectModelNumber}"
                                            />
                                            <div aura:id="modelNumber-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                        </lightning:layoutItem>
                                        <!-- Product Name -->
                                        <lightning:layoutItem alignmentBump="right" size="2">
                                            <lightning:input label="{!$Label.c.CCM_ProductName}" type="text" value="{!v.productName}" disabled="true"/>
                                        </lightning:layoutItem>
                                    </lightning:layout>
                                    <lightning:layout horizontalAlign="space">
                                        <!-- Customer Claim Reference Number -->
                                        <lightning:layoutItem alignmentBump="right" size="2">
                                            <lightning:input aura:id="claimReferenceNumber" label="{!$Label.c.CCM_CustomerClaimReferenceNumber}" type="text" value="{!v.customerClaimReferenceNumber}" />
                                            <!-- <div aura:id="claimReferenceNumber-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div> -->
                                        </lightning:layoutItem>
                                    </lightning:layout>
                                </div>
                            </div>
                            <!-- receipt info wrap-->
                            <!-- email不匹配 -->
                            <aura:if isTrue="{!v.emailValid}">
                                <div class="purchaseInfo">
                                    <div class="receipt-info">
                                        <p class="email-check">
                                            <span>{!$Label.c.CCM_WarrantyHasBeenRegistered}</span>
                                            <span>{!$Label.c.CCM_PleaseCheckEmail}</span>
                                        ​​</p>
                                    </div>
                                </div>
                            </aura:if>
                            <!-- 无权限或没有warranty记录 -->
                            <aura:if isTrue="{!(v.noPermission || v.noWarranty)}">
                                <div class="purchaseInfo">
                                    <div class="receipt-info">
                                        <aura:if isTrue="{!v.noPermission}">
                                            <p class="email-check">
                                                <span>{!$Label.c.CCM_WarrantyHasBeenRegistered}</span>
                                                <span>{!$Label.c.CCM_WarrantyStatus}: <span class="{!(v.warrantyStatus == 'Expired') ? 'isExpired' : ''}">{!v.warrantyStatus}</span></span>
                                            ​​</p>
                                        </aura:if>
                                        <aura:if isTrue="{!v.noWarranty}">
                                            <p class="no-warranty">{!$Label.c.CCM_NoWarrantyTips}<img style="margin-left: 12px;" src="{!$Resource.SystemIcon + '/RedIcon.png'}"/></p>
                                        </aura:if>
                                    </div>
                                </div>
                            </aura:if>
                            <!-- 有查看权限或创建权限 -->
                            <aura:if isTrue="{!(v.ownerPermission || v.viewPermission)}">
                                <div class="purchaseInfo">
                                    <div class="receipt-info">
                                        <div class="info-title">
                                            <p><strong>{!$Label.c.CCM_PlaceOfPurchase} :</strong></p>
                                            <p><strong>{!$Label.c.CCM_PurchaseDate} :</strong></p>
                                            <p><strong>{!$Label.c.CCM_ExpirationDate} :</strong></p>
                                            <p><strong>{!$Label.c.CCM_Receipt} :</strong></p>
                                        </div>
                                        <div class="info-value">
                                            <p>{!v.placeOfPurchase || ' '}</p>
                                            <p>{!v.purchaseDate || ' '}</p>
                                            <p>{!v.expiredDate || ' '}</p>
                                            <aura:if isTrue="{!v.isPortal}">
                                                <!-- portal端 -->
                                                <aura:if isTrue="{!v.ownerPermission}">
                                                    <p class="receipt-link" onclick="{!c.toReceiptDetail}">{! (v.receiptName ? $Label.c.CCM_ReceiptLink : $Label.c.CCM_NoReceipt)}</p>
                                                </aura:if>
                                                <aura:if isTrue="{!v.viewPermission}">
                                                    <p>{! (v.receiptName ? $Label.c.CCM_HasReceipt : $Label.c.CCM_NoReceipt)}</p>
                                                </aura:if>
                                                <aura:set attribute="else">
                                                    <!-- CRM端 -->
                                                    <p class="receipt-link" onclick="{!c.toReceiptDetail}">{! (v.receiptName ? $Label.c.CCM_ReceiptLink : $Label.c.CCM_NoReceipt)}</p>
                                                </aura:set>
                                            </aura:if>
                                        </div>
                                    </div>
                                    <!-- Upload your receipt -->
                                    <aura:if isTrue="{!v.isPortal}">
                                        <!-- portal端 -->
                                        <aura:if isTrue="{!v.ownerPermission}">
                                            <div class="upload-receipt-wrap">
                                                <div class="fileName-wrap" style="margin-top: 5px;">
                                                    <!-- <lightning:input aura:id="upload" class="upload-wrap" name="" type="file" label="Upload Your Receipt:" multiple="true" accept="image/png, .pdf" onchange="{!c.handleFilesChange}"/>
                                                    <aura:if isTrue="{!v.receiptUploadInfo.fileName}">
                                                        <p class="uploadFinished portal-color">
                                                            <span class="fileName">{!v.receiptUploadInfo.fileName}</span>
                                                            <a class="delete" onclick="{!c.deleteReceipt}">Delete</a>
                                                        </p>
                                                    </aura:if> -->
                                                    <lightning:fileUpload 
                                                        class="upload-wrap" 
                                                        label="{!$Label.c.CCM_UploadYourReceipt + ':'}"
                                                        name="fileUploader"
                                                        multiple="false"
                                                        accept="['.png', '.jpg', '.jpeg', '.pdf']"
                                                        recordId="{!v.recordId}"
                                                        onuploadfinished="{!c.handleUploadFinished}" 
                                                    />
                                                    <aura:if isTrue="{!v.receiptUploadInfo.fileName}">
                                                        <p class="uploadFinished">
                                                            <span class="fileName">{!v.receiptUploadInfo.fileName}</span>
                                                            <a class="delete" onclick="{!c.deleteReceipt}">{!$Label.c.CCM_Delete}</a>
                                                        </p>
                                                    </aura:if>
                                                </div>
                                            </div>
                                        </aura:if>
                                        <aura:set attribute="else">
                                            <!-- CRM端 -->
                                            <div class="upload-receipt-wrap">
                                                <div class="fileName-wrap" style="margin-top: 5px;">
                                                    <!-- <lightning:input aura:id="upload" class="upload-wrap" name="" type="file" label="Upload Your Receipt:" multiple="true" accept="image/png, .pdf" onchange="{!c.handleFilesChange}"/>
                                                    <aura:if isTrue="{!v.receiptUploadInfo.fileName}">
                                                        <p class="uploadFinished crm-color">
                                                            <span class="fileName">{!v.receiptUploadInfo.fileName}</span>
                                                            <a class="delete" onclick="{!c.deleteReceipt}">Delete</a>
                                                        </p>
                                                    </aura:if> -->
                                                    <lightning:fileUpload 
                                                        class="upload-wrap" 
                                                        label="{!$Label.c.CCM_UploadYourReceipt + ':'}"
                                                        name="fileUploader"
                                                        multiple="false"
                                                        accept="['.png', '.jpg', '.jpeg', '.pdf']"
                                                        recordId="{!v.recordId}"
                                                        onuploadfinished="{!c.handleUploadFinished}" 
                                                    />
                                                    <aura:if isTrue="{!v.receiptUploadInfo.fileName}">
                                                        <p class="uploadFinished">
                                                            <span class="fileName">{!v.receiptUploadInfo.fileName}</span>
                                                            <a class="delete" onclick="{!c.deleteReceipt}">{!$Label.c.CCM_Delete}</a>
                                                        </p>
                                                    </aura:if>
                                                </div>
                                            </div>
                                        </aura:set>
                                    </aura:if>
                                </div>
                            </aura:if>
                        </div>
                    </div>
                </article>
                <!-- Service Information -->
                <article class="slds-card slds-scrollable_y">
                    <div class="slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                            <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                title="{!$Label.c.CCM_ServiceInformation}">
                                    <span><strong>{!$Label.c.CCM_ServiceInformation}</strong></span>
                            </span>
                            </h2>
                        </div>
                    </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-wrap position-wrap">
                            <div>
                                <div class="slds-p-bottom_xx-small slds-size_1-of-4">
                                    <!-- Service Option -->
                                    <lightning:combobox
                                        aura:id="serviceType"
                                        value="{!v.serviceType}"
                                        options="{!v.serviceOption}"
                                        label="{!$Label.c.CCM_ServiceOption}"
                                        placeholder=""
                                    />
                                </div>
                                <div class="projectcode">
                                <aura:if isTrue="{!v.isNeedProject}">
                                    <div  aura:id="projectCode">
                                        <lightning:input aura:id="projectCode" label="{!$Label.c.CCM_ProjectCode}" value="{!v.projectCode}" onblur="{!c.getInfoBySerialNumber}" maxlength="255"/>
                                    </div>
                                    <div style="padding-left:5px;padding-top:12px;">
                                        <lightning:button class="projectbutton" label="+" variant="neutral"  onclick="{!c.showProjectInfo}"/>
                                    </div>
                                </aura:if>
                                </div>
                            </div>
                            <!-- distributor & dealer - Replacement Option & Repair Type-->
                            <aura:if isTrue="{!v.isPortal}">
                                <!-- portal -->
                                <aura:if isTrue="{!v.distributorOrDealer == 'Distributor'}">
                                    <!-- serviceType === Replacement -->
                                    <aura:if isTrue="{!v.serviceType == 'Replacement'}">
                                        <div class="slds-p-bottom_xx-small slds-size_1-of-4">
                                            <!-- Replacement Option -->
                                            <lightning:combobox
                                                aura:id="replacementType"
                                                value="{!v.replacementType}"
                                                options="{!v.replacementByDistributor}"
                                                label="{!$Label.c.CCM_ReplacementOption}"
                                                placeholder=""
                                            />
                                        </div>
                                        <!-- type === Repair -->
                                        <aura:set attribute="else">
                                            <div class="slds-p-bottom_xx-small slds-size_1-of-4">
                                                <!-- Repair Option -->
                                                <lightning:combobox
                                                    aura:id="repairType"
                                                    value="{!v.repairType}"
                                                    options="{!v.repairOption}"
                                                    label="{!$Label.c.CCM_RepairType}"
                                                    placeholder=""
                                                />
                                            </div>
                                        </aura:set>
                                    </aura:if>
                                    <aura:set attribute="else">
                                        <!-- serviceType === Replacement -->
                                        <aura:if isTrue="{!v.serviceType == 'Replacement'}">
                                            <div class="slds-p-bottom_xx-small slds-size_1-of-4">
                                                <!-- Replacement Option -->
                                                <lightning:combobox
                                                    aura:id="replacementType"
                                                    value="{!v.replacementType}"
                                                    options="{!v.replacementByDealer}"
                                                    label="{!$Label.c.CCM_ReplacementOption}"
                                                    placeholder=""
                                                />
                                            </div>
                                            <!-- type === Repair -->
                                            <aura:set attribute="else">
                                                <div class="slds-p-bottom_xx-small slds-size_1-of-4">
                                                    <!-- Repair Option -->
                                                    <lightning:combobox
                                                        aura:id="repairType"
                                                        value="{!v.repairType}"
                                                        options="{!v.repairOption}"
                                                        label="{!$Label.c.CCM_RepairType}"
                                                        placeholder=""
                                                    />
                                                </div>
                                            </aura:set>
                                        </aura:if>
                                    </aura:set>
                                </aura:if>
                                <!-- CRM -->
                                <aura:set attribute="else">
                                    <aura:if isTrue="{!v.serviceType == 'Replacement'}">
                                        <div class="slds-p-bottom_xx-small slds-size_1-of-4">
                                            <!-- Replacement Option -->
                                            <lightning:combobox
                                                aura:id="replacementType"
                                                value="{!v.replacementType}"
                                                options="{!v.replacementOption}"
                                                label="{!$Label.c.CCM_ReplacementOption}"
                                                placeholder=""
                                            />
                                        </div>
                                        <aura:set attribute="else">
                                            <div class="slds-p-bottom_xx-small slds-size_1-of-4">
                                                <!-- Repair Option -->
                                                <lightning:combobox
                                                    aura:id="repairType"
                                                    value="{!v.repairType}"
                                                    options="{!v.repairOption}"
                                                    label="{!$Label.c.CCM_RepairType}"
                                                    placeholder=""
                                                />
                                            </div>
                                        </aura:set>
                                    </aura:if>
                                </aura:set>
                            </aura:if>

                            <!-- Scenario 1 - serviceType == Replacement & replacementType == Credit Memo-->
                            <aura:if isTrue="{!v.scenarioType == 'Scenario 1'}">
                                <div class="slds-p-bottom_xx-small slds-size_1-of-4 required-wrap">
                                    <c:CCM_Community_LookUp 
                                        aura:id="address"
                                        fieldName="Address"
                                        fieldNameLabel="{!$Label.c.CCM_Address}"
                                        selectedValue="{!v.addressInfo}"
                                        class="field-required"
                                        isClaim="true"
                                        claimAddressType="ship"
                                        accId="{!v.accId}"
                                        onSelect="{!c.selectShipAddressInfo}"
                                    />
                                    <div aura:id="address-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </div>
                            </aura:if>
                            <!-- Scenario 2 - serviceType == Replacement & replacementType == For a free tools-->
                            <aura:if isTrue="{!v.scenarioType == 'Scenario 2'}">
                                <div class="slds-p-bottom_xx-small slds-size_1-of-4 required-wrap">
                                    <c:CCM_Community_LookUp 
                                        aura:id="address"
                                        fieldName="Address"
                                        fieldNameLabel="{!$Label.c.CCM_Address}"
                                        selectedValue="{!v.addressInfo}"
                                        class="field-required"
                                        isClaim="true"
                                        claimAddressType="ship"
                                        accId="{!v.accId}"
                                        onSelect="{!c.selectShipAddressInfo}"
                                    />
                                    <div aura:id="address-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                </div>
                                <div  class="{! (v.isPortal ? 'scenario2-wrap-portal' : 'scenario2-wrap')}">
                                    <div class="slds-p-bottom_xx-small slds-size_1-of-3 required-wrap" style="margin-left: 20px;">
                                        <lightning:input label="{!$Label.c.CCM_NewSerialNumber}" type="text" value="{!v.newSerialNumber}" minlength="15" maxlength="15" onblur="{!c.changeNewSerialNumber}" messageWhenTooLong="{!$Label.c.CCM_Enter15SerialNumberError}"/>
                                    </div>
                                </div>
                            </aura:if>
                            <!-- Scenario 3 - serviceType == Repair & repairType == Parts-->
                            <aura:if isTrue="{!v.scenarioType == 'Scenario 3'}">
                                <div class="scenario34-wrap">
                                    <!-- Failure Code -->
                                    <div class="slds-p-bottom_xx-small slds-size_1-of-3 required-wrap">
                                        <lightning:combobox
                                            aura:id="failureCode"
                                            value="{!v.failureCode}"
                                            options="{!v.failureCodeOptions}"
                                            label="{!$Label.c.CCM_FailureCode}"
                                            placeholder=""
                                            class="field-required"
                                            onblur="{!c.changeFailureCode}"
                                        />
                                        <div aura:id="failureCode-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                    </div>
                                    <!-- Repairable Parts -->
                                    <div class="slds-p-bottom_xx-small slds-size_1-of-3 required-wrap" style="margin-left: 20px;">
                                        <c:CCM_Community_LookUp 
                                            aura:id="repairableParts"
                                            fieldName="Repairable Parts"
                                            fieldNameLabel="{!$Label.c.CCM_RepairableParts}"
                                            selectedValue="{!v.repairablePartsInfo}"
                                            class="field-required"
                                            isProductCodeShow="true"
                                            isClaim="true"
                                            productId="{!v.productId}"
                                            accId="{!v.accId}"
                                            onSelect="{!c.selectRepairableParts}"
                                        />
                                        <div aura:id="repairableParts-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                    </div>
                                    <!-- Labor Time -->
                                    <div class="slds-p-bottom_xx-small slds-m-right_large slds-size_1-of-4" style="margin-left: 20px;">
                                        <div class="slds-form-element">
                                            <label class="slds-form-element__label" >
                                                {!$Label.c.CCM_LaborTime}
                                            </label>
                                            <div class="slds-form-element__control lineHeight" aura:id="laborHourPopUp">
                                                {!v.laborTime}&nbsp;{!$Label.c.CCM_Minutes}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </aura:if>
                            <!-- Scenario 4 - serviceType == Repair & repairType == Labor Time Only-->
                            <aura:if isTrue="{!v.scenarioType == 'Scenario 4'}">
                                <div class="scenario34-wrap">
                                    <!-- Failure Code -->
                                    <div class="slds-p-bottom_xx-small slds-size_1-of-3 required-wrap">
                                        <lightning:combobox
                                            aura:id="failureCode4"
                                            value="{!v.failureCode4}"
                                            options="{!v.failureCodeOptions}"
                                            label="{!$Label.c.CCM_FailureCode}"
                                            placeholder=""
                                            class="field-required"
                                            onblur="{!c.changeFailureCode4}"
                                        />
                                        <div aura:id="failureCode4-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                    </div>
                                    <!-- Repairable Parts -->
                                    <div class="slds-p-bottom_xx-small slds-size_1-of-3" style="margin-left: 20px;">
                                        <c:CCM_Community_LookUp 
                                            aura:id="repairableParts"
                                            fieldName="Repairable Parts"
                                            fieldNameLabel="{!$Label.c.CCM_RepairableParts}"
                                            isProductCodeShow="true"
                                            selectedValue="{!v.repairablePartsInfo}"
                                            isClaim="true"
                                            productId="{!v.productId}"
                                            accId="{!v.accId}"
                                            onSelect="{!c.selectRepairableParts}"
                                            isDisabled="true"
                                        />
                                    </div>
                                    <!-- Labor Time -->
                                    <div class="slds-p-bottom_xx-small slds-m-right_large slds-size_1-of-4" style="margin-left: 20px;">
                                        <div class="slds-form-element">
                                            <label class="slds-form-element__label">
                                                {!$Label.c.CCM_LaborTime}
                                            </label>
                                            <div class="slds-form-element__control lineHeight laborHoursInput" aura:id="laborHourInput">
                                                <lightning:input  label="" variant="label-hidden" value="{!v.overTimeHour}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen255Error}" maxlength="255"/>&nbsp;{!$Label.c.CCM_Minutes}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </aura:if>
                        </div>
                        <!-- Description -->
                        <div class="slds-grid slds-wrap slds-m-top--xx-small">
                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-size_1-of-1 required-wrap" style="margin-bottom: 20px;">
                                <lightning:textarea class="field-required" aura:id="description" name="" label="{!$Label.c.CCM_Description}" value="{!v.description}" onblur="{!c.changeDescription}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen500Error}" maxlength="500"/>
                                <div aura:id="description-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                            </div>
                        </div>
                        <!-- 添加parts表格 -->
                        <aura:if isTrue="{! (v.scenarioType == 'Scenario 3')}">
                            <div class="slds-wrap slds-m-bottom_medium">
                                <table aria-multiselectable="true" class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols slds-table_striped" role="grid">
                                    <thead>
                                        <tr class="slds-line-height_reset">
                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable smallWidth" scope="col">
                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                        <span class="slds-truncate" title="{!$Label.c.CCM_PartsName}">{!$Label.c.CCM_PartsName}</span>
                                                    </div>
                                                </a>
                                            </th>
                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable" scope="col">
                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                        <span class="slds-truncate" title="{!$Label.c.CCM_PartsNumber}">{!$Label.c.CCM_PartsNumber}</span>
                                                    </div>
                                                </a>
                                            </th>
                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                        <span class="slds-truncate" title="{!$Label.c.CCM_Quantity}">{!$Label.c.CCM_Quantity}</span>
                                                    </div>
                                                </a>
                                            </th>
                                            <!-- <aura:if isTrue="{!v.isNotDistributor}"> -->
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate" title="{!$Label.c.CCM_UnitPrice}">{!$Label.c.CCM_UnitPrice}</span>
                                                        </div>
                                                    </a>
                                                </th>
                                                <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                                                    <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                        <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                            <span class="slds-truncate" title="{!$Label.c.CCM_Total}">{!$Label.c.CCM_Total}</span>
                                                        </div>
                                                    </a>
                                                </th>
                                            <!-- </aura:if> -->
                                            <th aria-label="" aria-sort="none" class="slds-is-resizable slds-is-sortable largeWidth" scope="col">
                                                <a class="slds-th__action slds-text-link_reset" href="javascript:void(0);" role="button" tabindex="0">
                                                    <div class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    </div>
                                                </a>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <aura:iteration items="{!v.partsItemList}" var="partsItem" indexVar="index">
                                            <tr aria-selected="false" class="slds-hint-parent">
                                                <!-- Repairable Parts -->
                                                <th scope="row">
                                                    <div class="slds-truncate partsNumber" title="{!partsItem.partName}">
                                                        <aura:if isTrue="{!partsItem.type == 'additionalPart'}">
                                                            <c:CCM_Community_LookUp
                                                                class="{!index}"
                                                                aura:id="repairableParts"
                                                                fieldName="Repairable Parts"
                                                                fieldNameLabel="{!$Label.c.CCM_RepairableParts}"
                                                                isProductCodeShow="true"
                                                                selectedValue="{!partsItem.partInfo}"
                                                                isClaim="true"
                                                                productId="{!v.productId}"
                                                                accId="{!v.accId}"
                                                                onSelect="{!c.selectRepairablePartsForTable}"
                                                            />
                                                            <aura:set attribute="else">
                                                                {!partsItem.partName}
                                                            </aura:set>
                                                        </aura:if>
                                                    </div>
                                                </th>
                                                <!-- Parts Number -->
                                                <th scope="row">
                                                    <div class="slds-truncate" title="{!partsItem.itemNumber}">
                                                        {!partsItem.itemNumber}
                                                    </div>
                                                </th>
                                                <!-- quantity -->
                                                <th scope="row">
                                                    <div class="slds-truncate quantity" title="{!partsItem.quantity}" data-index="{!index}" data-parts="{!index}">
                                                        <lightning:input id="{!index}" class="partsInput" type="number" label="" value="{!partsItem.quantity}" onblur="{!c.calculatePartsTotalPrice}" disabled="{!v.partsItem.type == 'batterydiagnostic'}"/>
                                                    </div>
                                                </th>
                                                <!-- 单价 -->
                                                <th scope="row">
                                                    <div class="slds-truncate" title="{!partsItem.unitPrice}">
                                                        <aura:if isTrue="{!partsItem.noCalculate}">
                                                            NaN
                                                            <aura:set attribute="else">
                                                                <lightning:formattedNumber value="{!partsItem.unitPrice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                            </aura:set>
                                                        </aura:if>
                                                    </div>
                                                </th>
                                                <!-- 单行总价 -->
                                                <th scope="row">
                                                    <div class="slds-truncate" title="{!partsItem.total}">
                                                        <aura:if isTrue="{!partsItem.noCalculate}">
                                                            NaN
                                                            <aura:set attribute="else">
                                                                <lightning:formattedNumber value="{!partsItem.total}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                                            </aura:set>
                                                        </aura:if>
                                                    </div>
                                                </th>
                                                <!-- 删除 -->
                                                <th scope="row">
                                                    <div class="slds-truncate deleteBtn">
                                                        <lightning:icon id="{!index}" iconName="utility:delete" alternativeText="delete" size="x-small" onclick="{!c.handleDelete}" />
                                                    </div>
                                                </th>
                                            </tr>
                                        </aura:iteration>
                                    </tbody>
                                </table>
                                <div class="slds-text-align_right">
                                    <lightning:button class="slds-m-vertical_medium" label="{!$Label.c.CCM_AdditionalParts}" iconName="utility:add" iconPosition="left" variant="brand" onclick="{!c.addItionalParts}"/>
                                </div>
                                <!-- 爆炸图弹框 -->
                                <c:CCM_ExplosiveViewComponent
                                    productId="{!v.productId}"
                                    customerId="{!v.accId}"
                                    BtnName="{!$Label.c.CCM_ViewFromExplodedDiagram}"
                                    addSelect="true"
                                >
                                </c:CCM_ExplosiveViewComponent>
                            </div>
                        </aura:if>
                    </div>
                </article>
                <!-- Payment Information -->
                <article class="slds-card slds-m-top--medium" style="padding-bottom: 12px;">
                    <div class="slds-grid">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                        title="{!$Label.c.CCM_PaymentInformation}">
                                        <span><strong>{!$Label.c.CCM_PaymentInformation}</strong></span>
                                    </span>
                                </h2>
                            </div>
                        </header>
                    </div>
                    <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                        <div class="slds-p-bottom_xx-small slds-size_1-of-4 required-wrap">
                            <!-- Bill To -->
                            <c:CCM_Community_LookUp 
                                aura:id="billToAddress"
                                fieldName="Bill To"
                                fieldNameLabel="{!$Label.c.CCM_BillTo}"
                                selectedValue="{!v.billAddressInfo}"
                                class="field-required"
                                isClaim="true"
                                claimAddressType="bill"
                                accId="{!v.accId}"
                                onSelect="{!c.selectBillAddressInfo}"
                            />
                            <div aura:id="billToAddress-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                        </div>
                    </div>
                </article>
                <!-- Additional Information -->
                <aura:if isTrue="{!v.scenarioType == 'Scenario 3'}">
                    <article class="slds-card accordionCon">
                        <div>
                            <lightning:accordion allowMultipleSectionsOpen="true" activeSectionName="{! v.activeSections }" >
                                <lightning:accordionSection name="A" label="{!$Label.c.CCM_AdditionalInformation}">
                                    <aura:set attribute="body">
                                        <div class="slds-grid slds-wrap">
                                            <div class="slds-p-top_xx-small slds-p-bottom_xx-small slds-m-right_medium overtime">
                                                <lightning:input type='number' class="overtimeInput" name="" label="{!$Label.c.CCM_ActualTime}" value="{!v.overTimeHour}" onblur="{!c.changeOverTimeHour}"/><span class="hour">&nbsp;{!$Label.c.CCM_Minutes}</span>
                                            </div>
                                            <div class="{!(v.overTimeHour > 0 ? 'required-wrap slds-p-top_xx-small slds-p-bottom_xx-small slds-size_1-of-2' : 'slds-p-top_xx-small slds-p-bottom_xx-small slds-size_1-of-2')}">
                                                <lightning:input class="{!(v.overTimeHour > 0 ? 'field-required' : '')}" aura:id="overTimeDescription" name="" label="{!$Label.c.CCM_Explanation}" value="{!v.overTimeDescription}" onblur="{!c.changeOverTimeDescription}" messageWhenTooLong="{!$Label.c.CCM_NoMoreThen2000Error}" maxlength="2000"/>
                                                <div aura:id="overTimeDescription-error-required" class="error-text slds-hide">{!$Label.c.CCM_FieldRequired}</div>
                                            </div>
                                        </div>
                                    </aura:set>
                                </lightning:accordionSection>
                            </lightning:accordion>
                        </div>
                    </article>
                </aura:if>
                <!-- Reject Comments -->
                <aura:if isTrue="{!v.claimStatus == 'Rejected'}">
                    <article class="slds-card">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body">
                                <h2 class="slds-card__header-title section-header-title slds-section__title slds-theme--shade ">
                                    <span class="section-header-title slds-p-horizontal--small slds-truncate"
                                        title="$Label.c.CCM_Reject_Comments">
                                            <span><strong>{!$Label.c.CCM_Reject_Comments}</strong></span>
                                    </span>
                                </h2>
                            </div>
                        </header>
                        <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">
                            <span>{!v.rejectComments}</span>
                        </div>
                    </article>
                </aura:if>

                <!-- 价格计算 -->
                <div class="footer slds-p-vertical_large slds-float_right totalCon">
                    <div class="slds-border_bottom slds-p-bottom_x-small">{!$Label.c.CCM_LaborHours}: <strong>{!v.finialLaborHour}&nbsp;{!$Label.c.CCM_Mins} @ <lightning:formattedNumber value="{!v.laborRate}" style="currency" currencyCode="{!v.currencySymbol}" minimumFractionDigits="2"/></strong>/min</div>
                    <aura:if isTrue="{!v.isBatteryDiagnosticFee}">
                        <div class=""><p class="slds-border_bottom slds-p-vertical_x-small">Diagnostic Flat Rate: <strong><lightning:formattedNumber value="{!v.diagnosticFee}" style="currency" currencyCode="{!v.currencySymbol}" minimumFractionDigits="2"/></strong></p></div>
                    </aura:if>
                    <div class=""><p class="slds-border_bottom slds-p-vertical_x-small">{!$Label.c.CCM_LaborCostSubtotal}: <strong><lightning:formattedNumber value="{!(v.laborCostSubtotal)}" style="currency" currencyCode="{!v.currencySymbol}" minimumFractionDigits="2"/></strong></p></div>
                    <div class=""><p class="slds-border_bottom slds-p-vertical_x-small">{!$Label.c.CCM_MaterialCostSubtotal}: <strong><lightning:formattedNumber value="{!v.partsCost}" style="currency" currencyCode="{!v.currencySymbol}" minimumFractionDigits="2"/></strong></p></div>
                    <div class="slds-p-vertical_x-small"><strong>{!$Label.c.CCM_Total}: <lightning:formattedNumber value="{!v.totalPrice}" style="currency" currencyCode="{!v.currencySymbol}" minimumFractionDigits="2"/></strong></div>
                </div>
                <div class="slds-m-top_large btn-wrap">
                    <lightning:button class="" label="{!$Label.c.CCM_Cancel}" title="{!$Label.c.CCM_Cancel}" onclick="{!c.handleCancel}" />
                    <aura:if isTrue="{!v.claimType == 'create'}">
                        <lightning:button class="btn-margin" variant="brand" label="{!$Label.c.CCM_Save}" title="{!$Label.c.CCM_Save}" onclick="{!c.handleSaveAsDraft}" />
                        <lightning:button class="btn-margin" variant="brand" label="{!$Label.c.CCM_Submit}" title="{!$Label.c.CCM_Submit}" onclick="{!c.submitConfirm}" />
                        <aura:set attribute="else">
                            <aura:if isTrue="{!v.showNewSN}">
                                <lightning:button class="btn-margin" variant="brand" label="{!$Label.c.CCM_AddNewSN}" onclick="{!c.openNewSNPopup}" />
                            </aura:if>
                            <lightning:button class="btn-margin" variant="brand" label="{!$Label.c.CCM_Copy}" title="{!$Label.c.CCM_Copy}" onclick="{!c.copyClaim}" />
                        </aura:set>
                    </aura:if>
                    <aura:if isTrue="{!v.showNewPOButton}">
                        <lightning:button class="btn-margin" variant="brand" label="{!$Label.c.CCM_PlaceOrder}" title="{!$Label.c.CCM_PlaceOrder}" onclick="{!c.placeNewOrder}" />
                    </aura:if>
                </div>
            </section>


            <div class="slds-m-around_xx-large">
            <!--Project information dialog画面-->   
            <aura:if isTrue="{!v.isOpen}">
                <!--###### MODAL BOX Start######--> 
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container">
                        <!-- ###### MODAL BOX HEADER Start ######-->
                        <header class="slds-modal__header">
                            <lightning:buttonIcon iconName="utility:close"
                                                onclick="{! c.closeModel }"
                                                alternativeText="close"
                                                variant="bare-inverse"
                                                class="slds-modal__close"/>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium" style="padding:10px;border-bottom-style:solid;border-bottom-color:#D4D4D4;border-bottom-width:2px;">{!$Label.c.CCM_ProjectInformation}</h2>
                        </header>
                        <!--###### MODAL BOX BODY Part Start######-->
                        <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                            


                <!--Project Code Dialog Table展示-->
                <div>
                <div class="slds-card__body slds-card__body_inner slds-p-left_small slds-p-right_small">

                        <div class="container tb_product_list" style="padding: 0px;height: auto;">
                        <table aria-multiselectable="true"
                            class="slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols"
                            role="grid">
                            <thead>
                                <tr class="slds-line-height_reset">
                                        <th aria-label="" aria-sort="none"
                                            class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                            style="width:2%;">
                                            <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                <div
                                                    class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate"></span>
                                                </div>
                                            </a>
                                        </th>
                                        <th aria-label="" aria-sort="none"
                                            class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                            style="width:11%;">
                                            <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                <div
                                                    class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate">{!$Label.c.CCM_ProjectName}</span>
                                                </div>
                                            </a>
                                        </th>
                                        <th aria-label="" aria-sort="none"
                                            class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                            style="width:8%;">
                                            <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                <div
                                                    class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate">{!$Label.c.CCM_ProjectCode}</span>
                                                </div>
                                            </a>
                                        </th>
                                        <th aria-label="" aria-sort="none"
                                            class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                            style="width:8%;">
                                            <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                <div
                                                    class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate">{!$Label.c.CCM_Solution}</span>
                                                </div>
                                            </a>
                                        </th>
                                        <th aria-label="" aria-sort="none"
                                            class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                            style="width:8%;">
                                            <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                <div
                                                    class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate">{!$Label.c.CCM_Product}</span>
                                                </div>
                                            </a>
                                        </th>
                                        <th aria-label="" aria-sort="none"
                                            class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                            style="width:15%;">
                                            <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                <div
                                                    class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate">{!$Label.c.CCM_Reason}</span>
                                                </div>
                                            </a>
                                        </th>
                                        <th aria-label="" aria-sort="none"
                                            class="slds-is-resizable slds-is-sortable smallWidth" scope="col"
                                            style="width:18%;">
                                            <a class="slds-th__action slds-text-link_reset" role="button" tabindex="0">
                                                <div
                                                    class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate">
                                                    <span class="slds-truncate">{!$Label.c.CCM_ProjectSNRange}</span>
                                                </div>
                                            </a>
                                        </th>
                                </tr>
                            </thead>
                            <tbody>
                                <aura:iteration items="{!v.projectList}" var="projectItem" indexVar="index">
                                    <tr>
                                        <td scope="row">
                                            <div class="slds-truncate" style="text-align: left;">
                                                <lightning:input type="checkbox" 
                                                                data-record="{!index}" 
                                                                aura:id="doctorId"
                                                                value="{!projectItem.projectCode}" 
                                                                onchange ="{!c.selectProject}"
                                                                disabled = "{!and(v.selectedProject != projectItem.projectCode,v.selectedProject != '')}"/>
                                            </div>
                                        </td>
                                        
                                        <td scope="row">
                                            <div class="slds-truncate" style="text-align: center;">
                                                {!projectItem.projectName}
                                            </div>
                                        </td>
                                        <td scope="row">
                                            <div class="slds-truncate clear-user-agent-styles">
                                                <div class="icon-wrap" style="text-align: center;">
                                                    <span>{!projectItem.projectCode}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td scope="row">
                                            <div class="slds-truncate" style="text-align: center;">
                                                {!projectItem.solution}
                                            </div>
                                        </td>
                                        <td scope="row">
                                            <div class="slds-truncate" style="text-align: center;">
                                                {!projectItem.product}
                                            </div>
                                        </td>
                                        <td scope="row">
                                            <div class="slds-truncate" style="text-align: center;">
                                                {!projectItem.reason}
                                            </div>
                                        </td>
                                        <td scope="row">
                                            <div class="slds-truncate" style="text-align: center;">
                                                {!projectItem.projectSnRange}
                                            </div>
                                        </td>
                                    </tr>
    
                                </aura:iteration>
                            </tbody>
                        </table>
                    </div>
            </div>
            </div> 

            </div>
                <!--###### MODAL BOX FOOTER 按钮######-->
                <footer class="slds-modal__footer">
                    <lightning:button variant="neutral" 
                                    label="{!$Label.c.CCM_Cancel}"
                                    title="{!$Label.c.CCM_Cancel}"
                                    onclick="{! c.closeModel }"/>
                    <lightning:button variant="brand" 
                                    label="{!$Label.c.CCM_AddToProjectCode}"
                                    title="{!$Label.c.CCM_AddToProjectCode}"
                                    onclick="{! c.setProjectCode}"/>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
        <!--###### MODAL BOX Part END Here ######-->
            
        </aura:if>
        </div>
        </div>
        <!-- new claim 弹框 -->
        <aura:if isTrue="{!v.showNewSNFlag}">
            <div class="dialog-wrap">
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container" style="width: 40rem !important; max-width: 60rem !important; height:auto !important; transform: translate(0%, 50%);">
                        <div class="modal-header slds-modal__header">
                            <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.closeNewSNPopup}">
                                <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                                <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                            </button>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_AddNewSN}</h2>
                        </div>
                        <div class="slds-modal__content dialog-content-wrap">
                            <lightning:layout horizontalAlign="space">
                                <!-- Select Customer -->
                                <lightning:layoutItem alignmentBump="center" size="8">
                                    <lightning:input class="new-sn-btn" label="{!$Label.c.CCM_NewSerialNumber}" type="text" value="{!v.newSerialNumber}" minlength="15" maxlength="15" onblur="{!c.changeNewSerialNumber}" messageWhenTooLong="{!$Label.c.CCM_Enter15SerialNumberError}"/>
                                </lightning:layoutItem>
                            </lightning:layout>
                        </div>
                        <footer class="slds-modal__footer">
                            <lightning:button class="" variant="brand"  label="{!$Label.c.CCM_Save}" onclick="{!c.saveNewSNEvent}"/>
                            <lightning:button class="" variant="brand-outline"  label="{!$Label.c.CCM_Cancel}" onclick="{!c.closeNewSNPopup}"/>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </aura:if>
    </div>
</aura:component>