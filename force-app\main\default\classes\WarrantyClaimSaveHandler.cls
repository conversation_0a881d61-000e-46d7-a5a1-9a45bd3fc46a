/**************************************************************************************************
 * Name: WarrantyClaimBasicHandler
 * Object: Lead
 * Purpose:  Warranty Claim 保存与查询,审批
 * Author:Aria W Zhong
 * Create Date: 2023-10-07
 * Modify History:
 **************************************************************************************************/
public without sharing class WarrantyClaimSaveHandler{
    public static String saveClaim(String saveStr, String claimId){
        Map<String, String> returnMap = new Map<String, String>();
        Warranty_Claim__c claim = new Warranty_Claim__c(
        );
        try{
            claim = getClaim(saveStr, claimId);
            upsert claim;
            // delete previous items
            deletePreviousClaimItems(claim.Id);
            List<Warranty_Claim_Item__c> claimItemList = getClaimItemForSave(saveStr, String.valueOf(claim.Id));
            upsert claimItemList;
            returnMap.put('claimId', claim.Id);
            returnMap.put('isSuccess', 'True');
        } catch (Exception e){
            returnMap.put('isSuccess', 'False');
            returnMap.put('message', e.getLineNumber() + '-' + e.getMessage());

        }
        return JSON.serialize(returnMap);
    }
    public static Warranty_Claim__c getClaim(String saveStr, String claimId){
        WarrantyClaimEntity entity = (WarrantyClaimEntity)Json.deserialize(saveStr, WarrantyClaimEntity.class);
        Warranty_Claim__c claim = new Warranty_Claim__c(
        );
        claim.Claim_Status__c = String.isBlank(entity.claimStatus) ? 'draft' : entity.claimStatus;
        claim.Payment_Status__c = entity.paymentStatus;
        claim.Email_Address__c = entity.emailAddress;
        claim.Drop_off_Date__c = entity.dropOffDate;
        claim.Repair_Date__c = entity.repairDate;
        claim.Brand__c = entity.brand;
        claim.Model_Number__c = entity.modelNumber;
        claim.Serial_Number__c = entity.serialNumber;
        claim.User_Type__c = entity.userType;
        claim.Product__c = String.isBlank(entity.productId) ? null : entity.productId;
        claim.Product_Name__c = entity.productName;
        claim.Consumer__c = String.isBlank(entity.consumerId) ? null : entity.consumerId;
        claim.Warranty_Item__c = String.isBlank(entity.warrantyId) ? null : entity.warrantyId;
        claim.Receipt_Link__c = entity.receiptLink;
        claim.Place_Of_Purchase__c = entity.placeOfPurchase;
        claim.Purchase_Date__c = entity.purchaseDate;
        claim.Expired_Date__c = entity.expiredDate;
        claim.Receipt_Name__c = entity.receiptName;
        claim.Receipt_Lost__c = entity.receiptLost;
        claim.Basic_Permission__c = entity.basicPermission;
        claim.Service_Option__c = entity.serviceOption;
        claim.Replacement_Option__c = entity.serviceOption == 'Replacement' ? entity.replacementOption : '';
        claim.Repair_Type__c = entity.serviceOption == 'Repair' ? entity.repairType : '';
        claim.Description__c = entity.description;
        claim.Failure_Code__c = entity.serviceOption == 'Repair' ? entity.failureCode : '';
        claim.Bill_Address_Name__c = entity.billAddressName;
        claim.Bill_Address__c = String.isBlank(entity.billAddressId) ? null : entity.billAddressId;
        claim.Ship_Address_name__c = entity.shipAddressName;
        claim.Ship_Address__c = String.isBlank(entity.shipAddressId) ? null : entity.shipAddressId;
        claim.Explanation__c = entity.Explanation;
        claim.Dealer_Name__c = String.isBlank(entity.CustomerId) ? null : entity.CustomerId;
        claim.Distributor_Or_Dealer__c = entity.DistributorOrDealer;
        claim.CurrencyIsoCode = entity.currencyIsoCode;
        claim.Project__c = String.isBlank(entity.projectId) ? null : entity.projectId;
        claim.Project_Code__c = entity.projectCode;
        claim.New_Serial_Number__c = entity.NewSerialNumber;
        //时间金钱
        claim.Labor_Rate__c = String.isNotBlank(entity.laborRate) ? Decimal.valueOf(entity.laborRate) : 0;
        claim.Actual_Labor_Rate__c = String.isNotBlank(entity.laborRate) ? Decimal.valueOf(entity.laborRate) : 0;
        claim.Labor_Input_Time__c = String.isBlank(entity.actualTime) ? 0:Decimal.valueOf(entity.actualTime);
        if (string.isNotBlank(claimId)){
            claim.Id = claimId;
        } else{
            claim.Claim_Date__c = Date.today();
        }
        claim.Customer_Claim_Reference_Number__c = entity.customerClaimReferenceNumber;
        claim.Diagnostic_Fee__c = entity.diagnosticFee != null ? entity.diagnosticFee : 0;
        return claim;
    }
    public static List<Warranty_Claim_Item__c> getClaimItemForSave(String saveStr, String claimId){
        WarrantyClaimEntity entity = (WarrantyClaimEntity)Json.deserialize(saveStr, WarrantyClaimEntity.class);
        //item
        List<Warranty_Claim_Item__c> claimItemList = new List<Warranty_Claim_Item__c>();
        //获取原价
        if (entity.partList.size() > 0){
            for (WarrantyClaimItemEntity itemEntity : entity.partList){
                Warranty_Claim_Item__c claimItem = new Warranty_Claim_Item__c(
                );
                claimItem.Warranty_Claim__c = String.isBlank(claimId) ? null : claimId;
                claimItem.Part__c = String.isBlank(itemEntity.partId) ? null : itemEntity.partId;
                claimItem.Part_Number__c = itemEntity.partNumber;
                claimItem.Quantity__c = itemEntity.quantity;
                claimItem.Type__c = itemEntity.type;
                claimItem.No_Caculate__c = itemEntity.noCalculate == null ? false : itemEntity.noCalculate;
                //标准时间与单价
                claimItem.labor_Time__c = String.isNotBlank(itemEntity.laborTime) ? Decimal.valueOf(itemEntity.laborTime) : 0;
                claimItem.Unit_Price__c = itemEntity.unitPrice;
                //实际时间与单价
                claimItem.Actual_Price__c = itemEntity.unitPrice;
                // if (string.isNotBlank(itemEntity.claimItemId)){
                //     claimItem.Id = String.isBlank(itemEntity.claimItemId) ? null : itemEntity.claimItemId;
                // }
                claimItemList.add(claimItem);
            }
        }
        return claimItemList;
    }
    public static List<Warranty_Claim_Item__c> getClaimItemForSubmit(String saveStr, String claimId, Warranty_Claim__c claim){
        WarrantyClaimEntity entity = (WarrantyClaimEntity)Json.deserialize(saveStr, WarrantyClaimEntity.class);
        //item
        List<Warranty_Claim_Item__c> claimItemList = new List<Warranty_Claim_Item__c>();
        if (entity.partList.size() > 0){
            //获取价格
            List<String> lstProductIds = new List<String>();
            List<String> lstProductIds2 = new List<String>();
            Map<String, Double> mapProdictId2Qty = new Map<String, Double>();
            for (WarrantyClaimItemEntity itemEntity : entity.partList){
                lstProductIds.add(itemEntity.partId);
                lstProductIds2.add(itemEntity.partId);
                mapProdictId2Qty.put(itemEntity.partId, 1);
            }
            System.debug('lstProductIds1:=>' + JSON.serialize(lstProductIds));
            Map<String, Map<String, Object>> mapProductId2Values = new Map<String, Map<String, Object>>();
            mapProductId2Values = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfoByDownload(entity.CustomerId, lstProductIds, claim.Claim_Date__c, mapProdictId2Qty);
            //获取Product对应pricebook
            System.debug('claim:=>' + JSON.serialize(claim));
            System.debug('lstProductIds2:=>' + JSON.serialize(lstProductIds));
            Map<String, String> pbMap = CCM_GetProductInfoUtil.getPriceName(lstProductIds2, claim.Claim_Date__c, claim.CurrencyIsoCode, claim.Dealer_Name__c);
            system.debug('pbMap--->' + JSON.serialize(pbMap));
            for (WarrantyClaimItemEntity itemEntity : entity.partList){
                Warranty_Claim_Item__c claimItem = new Warranty_Claim_Item__c(
                );
                claimItem.Warranty_Claim__c = String.isBlank(claimId) ? null : claimId;
                claimItem.Part__c = String.isBlank(itemEntity.partId) ? null : itemEntity.partId;
                claimItem.Part_Number__c = itemEntity.partNumber;
                claimItem.Quantity__c = itemEntity.quantity;
                claimItem.Type__c = itemEntity.type;
                claimItem.No_Caculate__c = itemEntity.noCalculate == null ? false : itemEntity.noCalculate;
                //标准时间与单价
                claimItem.labor_Time__c = String.isNotBlank(itemEntity.laborTime) ? Decimal.valueOf(itemEntity.laborTime) : 0;
                claimItem.Unit_Price__c = itemEntity.unitPrice;
                //实际时间与单价
                claimItem.Actual_Price__c = itemEntity.unitPrice;
                Map<String, Object> mapFeild2Value = mapProductId2Values.get(itemEntity.partId);
                system.debug('mapFeild2Value--->' + mapFeild2Value);
                if (mapFeild2Value != null){
                    claimItem.Original_Price__c = (Decimal) mapFeild2Value.get(CCM_Constants.LIST_PRICE);
                    claimItem.Stand_Discount_Value__c = (Decimal) mapFeild2Value.get(CCM_Constants.STAND_DISCOUNT);
                    claimItem.Application_Method__c = (String) mapFeild2Value.get(CCM_Constants.APPLICATION_METHOD);
                }
                // if (string.isNotBlank(itemEntity.claimItemId)){
                //     claimItem.Id = String.isBlank(itemEntity.claimItemId) ? null : itemEntity.claimItemId;
                // }
                if (pbMap.containsKey(itemEntity.partId)){
                    claimItem.PriceBook_Name__c = pbMap.get(itemEntity.partId);
                }
                claimItemList.add(claimItem);
            }
        }
        return claimItemList;
    }
    //检查是否有资格提交
    public static Map<String, String> checkSubmit(String saveStr, String claimId){
        boolean flag = true;
        WarrantyClaimEntity entity = (WarrantyClaimEntity)Json.deserialize(saveStr, WarrantyClaimEntity.class);
        //item
        List<WarrantyClaimItemEntity> claimItemList = new List<WarrantyClaimItemEntity>();
        String ProductStr = '';
        //获取原价
        if (entity.partList.size() > 0){
            for (WarrantyClaimItemEntity itemEntity : entity.partList){
                if (itemEntity.noCalculate){
                    flag = false;
                    ProductStr += itemEntity.partNumber + ',';
                }
            }
        }
        Map<String, String> returnMap = new Map<String, String>();
        if (!flag){
            ProductStr += Label.Can_Not_find_Price;
            returnMap.put('message', ProductStr);
            returnMap.put('isSuccess', 'False');
        } else{
            returnMap.put('isSuccess', 'True');
        }

        return returnMap;
    }
    public static String submitClaim(String saveStr, String claimId){
        Map<String, String> returnMap = new Map<String, String>();
        Warranty_Claim__c claim = new Warranty_Claim__c(
        );
        try{
            //检查是否有资格提交
            Map<String, String> checkMap = checkSubmit(saveStr, claimId);
            if (checkMap.get('isSuccess') == 'False'){
                returnMap.put('isSuccess', 'False');
                returnMap.put('message', checkMap.get('message'));
            } else{
                claim = getClaim(saveStr, claimId);
                claim.Claim_Status__c = 'Submitted';
                upsert claim;
                deletePreviousClaimItems(claim.Id);
                List<Warranty_Claim_Item__c> claimItemList = getClaimItemForSubmit(saveStr, String.valueOf(claim.Id), claim);
                upsert claimItemList;
                //生成warranty order
                if (claim.Service_Option__c == 'Replacement' && claim.Replacement_Option__c == 'For a free tools'){
                    Map<String, String> poReturnMap = CCM_WarrantyPurchaseOrderCtl.generateWarrantyOrder(claim.Id);
                    if (poReturnMap.get('status') == CCM_Constants.ERROR){
                        returnMap.put('isSuccess', 'False');
                        returnMap.put('message', poReturnMap.get('message'));
                    }

                }

                //调用审批方法
                // 新建申请
                Approval.ProcessSubmitRequest req = new Approval.ProcessSubmitRequest();
                // 审批记录
                req.setObjectId(claim.Id);
                // 提交人
                req.setSubmitterId(UserInfo.getUserId());
                req.setProcessDefinitionNameOrId('Claim_Approval');
                req.setComments('Submitting request for approval.');
                Approval.ProcessResult result = Approval.process(req);
            }
            //判断最终结果
            if (!(returnMap.containsKey('isSuccess') && returnMap.get('isSuccess') == 'False')){
                returnMap.put('isSuccess', 'True');
                returnMap.put('claimId', claim.Id);
            }

        } catch (Exception e){
            returnMap.put('isSuccess', 'False');
            returnMap.put('message', e.getLineNumber() + '-' + e.getMessage());
        }
        return JSON.serialize(returnMap);
    }

    private static void deletePreviousClaimItems(String claimId) {
        List<Warranty_Claim_Item__c> items = [SELECT Id FROM Warranty_Claim_Item__c WHERE Warranty_Claim__c = :claimId];
        if(!items.isEmpty()) {
            delete items;
        }
    }

    public static String queryClaimDetail(String claimId){
        //Part_Number__c,labor_time__c
        Warranty_Claim__c claim = [select (select No_Caculate__c, labor_Time__c, Part__c, Part_Name__c, Quantity__c, Cost_Actual__c, Type__c, Actual_Price__c, Warranty_Claim__c, Part_Number__c, Id
                                           from Warranty_Claim_Items__r), New_Serial_Number__c, Warranty_Purchase_Order__c, Warranty_Purchase_Order__r.Name, Final_Labor_Rate__c, Repair_Date__c, Labor_Standard_Time__c, Model_Number__c, currencyIsoCode, Claim_Status__c, Product_Name__c, Payment_Status__c, Email_Address__c, Drop_off_Date__c, Brand__c, Serial_Number__c, User_Type__c, Product__c, Consumer__c, Warranty_Item__c, Receipt_Link__c, Place_Of_Purchase__c, Purchase_Date__c, Expired_Date__c, Receipt_Name__c, Receipt_Lost__c, Basic_Permission__c, Service_Option__c, Replacement_Option__c, Repair_Type__c, Description__c, Failure_Code__c, Failure_Description__c, Bill_Address_Name__c, Bill_Address__c, Ship_Address_name__c, Ship_Address__c, Labor_Input_Time__c, Explanation__c, Labor_Final_Time__c, Labor_Final_Cost__c, Material_Actual_Cost__c, Total_Actual__c, Total_Actual_To_Oracle__c, Dealer_Name__c, Distributor_Or_Dealer__c, Id, Is_Mass_Upload__c,
                                           Warranty_Item__r.Warranty_Status__c, Customer_Claim_Reference_Number__c, Diagnostic_Fee__c
                                   from Warranty_Claim__c
                                   where Id = :claimId];
        List<Warranty_Claim__c> claimList = new List<Warranty_Claim__c>();
        claimList.add(claim);
        Map<Id, String> claimRejectCommentsMap = getClaimRejectCommentsMap(claimList);
        WarrantyClaimEntity entity = new WarrantyClaimEntity();
        entity.claimStatus = claim.Claim_Status__c;
        entity.paymentStatus = claim.Payment_Status__c;
        entity.emailAddress = claim.Email_Address__c;
        entity.dropOffDate = claim.Drop_off_Date__c;
        entity.repairDate = claim.Repair_Date__c;
        entity.brand = claim.Brand__c;
        entity.productName = claim.Product_Name__c;
        entity.serialNumber = claim.Serial_Number__c;
        entity.modelNumber = claim.Model_Number__c;
        entity.userType = claim.User_Type__c;
        entity.productId = claim.Product__c;
        entity.consumerId = claim.Consumer__c;
        entity.warrantyId = claim.Warranty_Item__c;
        entity.receiptLink = claim.Receipt_Link__c;
        entity.placeOfPurchase = claim.Place_Of_Purchase__c;
        entity.purchaseDate = claim.Purchase_Date__c;
        entity.expiredDate = claim.Expired_Date__c;
        entity.receiptName = claim.Receipt_Name__c;
        entity.receiptLost = claim.Receipt_Lost__c;
        entity.basicPermission = claim.Basic_Permission__c;
        entity.serviceOption = claim.Service_Option__c;
        entity.replacementOption = claim.Replacement_Option__c;
        entity.repairType = claim.Repair_Type__c;
        entity.description = claim.Description__c;
        if(String.isBlank(entity.description)) {
            entity.description = claim.Failure_Description__c;
        }
        entity.failureCode = claim.Failure_Code__c;
        entity.billAddressName = claim.Bill_Address_Name__c;
        entity.billAddressId = claim.Bill_Address__c;
        entity.shipAddressName = claim.Ship_Address_name__c;
        entity.shipAddressId = claim.Ship_Address__c;
        entity.Explanation = claim.Explanation__c;
        entity.CustomerId = claim.Dealer_Name__c;
        entity.DistributorOrDealer = claim.Distributor_Or_Dealer__c;
        entity.currencyIsoCode = claim.CurrencyIsoCode;
        entity.NewSerialNumber = claim.New_Serial_Number__c;
        entity.isMassUpload = claim.Is_Mass_Upload__c;
        if(claimRejectCommentsMap.containsKey(claim.Id)) {
            entity.rejectComments = claimRejectCommentsMap.get(claim.Id);
            if(String.isBlank(entity.rejectComments)) {
                entity.rejectComments = 'no comments';
            }
        }
        //时间金钱相关
        entity.actualTime = String.valueOf(claim.Labor_Input_Time__c);
        entity.laborRate = String.valueOf(claim.Final_Labor_Rate__c);
        entity.totalLaborHours = String.valueOf(claim.Labor_Final_Time__c);
        entity.standardPartsHour = String.valueOf(claim.Labor_Standard_Time__c);
        entity.laborCost = claim.Labor_Final_Cost__c;
        entity.materialCost = claim.Material_Actual_Cost__c;
        entity.Total = claim.Total_Actual_To_Oracle__c;
        entity.warrantyStatus = claim.Warranty_Item__r.Warranty_Status__c;
        entity.customerClaimReferenceNumber = claim.Customer_Claim_Reference_Number__c;
        entity.diagnosticFee = claim.Diagnostic_Fee__c;
        //warranty order
        if (String.isNotBlank(claim.Warranty_Purchase_Order__c)){
            entity.warrantyOrderName = claim.Warranty_Purchase_Order__r.Name;
            entity.warrantyOrderId = claim.Warranty_Purchase_Order__c;
            //审批完成后会有 order数据
            if (claim.Claim_Status__c == 'Approved'){
                List<Order> orderList = [select Id, PO_No__c, Order_Number__c, Warranty_Purchase_Order__c
                                         from order
                                         where Warranty_Purchase_Order__c = :claim.Warranty_Purchase_Order__c];
                if (orderList.size() > 0){
                    entity.warrantyOrderId = orderList.get(0).Id;
                    entity.warrantyOrderName = orderList.get(0).Order_Number__c;
                }
            }
        }

        List<WarrantyClaimItemEntity> claimItemEntityList = new List<WarrantyClaimItemEntity>();
        for (Warranty_Claim_Item__c claimItem : claim.Warranty_Claim_Items__r){
            WarrantyClaimItemEntity itemEntity = new WarrantyClaimItemEntity();
            itemEntity.partId = claimItem.Part__c;
            itemEntity.partNumber = claimItem.Part_Number__c;
            itemEntity.partName = claimItem.Part_Name__c;
            itemEntity.quantity = claimItem.Quantity__c;
            itemEntity.type = claimItem.Type__c;
            itemEntity.laborTime = String.valueOf(claimItem.labor_Time__c);
            itemEntity.unitPrice = claimItem.Actual_Price__c;
            itemEntity.total = claimItem.Cost_Actual__c;
            itemEntity.claimItemId = claimItem.Id;
            itemEntity.noCalculate = claimItem.No_Caculate__c;
            claimItemEntityList.add(itemEntity);
        }
        entity.partList = claimItemEntityList;
        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('isSuccess', 'True');
        returnMap.put('Data', entity);
        return JSON.serialize(returnMap);
    }
    public static String deleteClaim(String claimId){
        List<Warranty_Claim_Item__c> claimList = [select id
                                                  from Warranty_Claim_Item__c
                                                  where Warranty_Claim__c = :claimId];
        delete claimList;
        Warranty_Claim__c claim = new Warranty_Claim__c(
            Id = claimId
        );
        delete claim;
        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('isSuccess', 'True');
        return JSON.serialize(returnMap);
    }

    public static String queryClaimList(String accountId, Integer pageNumber, Integer allPageSize, String modelNumber, String status, String invoiceStatus, String serialNumber, String name, String companyName, String claimReferenceNumber){
        List<Warranty_Claim__c> claimList = new List<Warranty_Claim__c>();
        Map<String, Order> orderMap = new Map<String, Order>();
        String claimquery = 'select Id,Is_Mass_Upload__c,CRM_Order_Number__c,Dealer_Name__c,Consumer__r.Fleet_Manager__c,consumer__r.Consumer_Company_Formula__c,Name, CurrencyIsoCode, Claim_Status__c, Payment_Status__c, Model_Number__c, Serial_Number__c, Labor_Final_Cost__c, Material_Actual_Cost__c,Total_Actual__c, Total_Actual_To_Oracle__c,Warranty_Purchase_Order__c,Warranty_Purchase_Order__r.Name, Customer_Claim_Reference_Number__c  from Warranty_Claim__c where Claim_Status__c <> null';
        Boolean isportal = WarrantyClaimUtil.IsPortal();
        String salesChannel = 'Dealer';
        String country = '';

        if (isportal && String.isNotBlank(accountId) && salesChannel == 'Dealer'){
            claimquery += ' and Dealer_Name__c =\'' + accountId + '\'';
        }

        if(isportal && salesChannel == 'Distributor') {
            claimquery += ' and (Dealer_Name__r.Country =\'' + country + '\' or Consumer__r.ShippingCountry = \'' + country + '\')';
        }

        if (String.isNotBlank(modelNumber)){
            modelNumber = '%' + modelNumber + '%';
            claimquery += ' and Model_Number__c like\'' + modelNumber + '\'';
        }
        if (String.isNotBlank(invoiceStatus)){
            invoiceStatus = '%' + invoiceStatus + '%';
            claimquery += ' and Payment_Status__c like\'' + invoiceStatus + '\'';
        }
        if (String.isNotBlank(serialNumber)){
            serialNumber = '%' + serialNumber + '%';
            claimquery += ' and Serial_Number__c like\'' + serialNumber + '\'';
        }
        if (String.isNotBlank(status)){
            claimquery += ' and Claim_Status__c =\'' + status + '\'';
        }
        if (String.isNotBlank(name) ){
            name = '%' + name + '%';
            claimquery += ' and Name like \'' + name + '\'';
        }
        if (String.isNotBlank(companyName)){
            companyName = '%' + companyName + '%';
            claimquery += ' and Consumer__r.Name like \'' + companyName + '\'';
        }
        if(String.isNotBlank(claimReferenceNumber)) {
            claimReferenceNumber = '%' + claimReferenceNumber + '%';
            claimquery += ' and Customer_Claim_Reference_Number__c like \'' + claimReferenceNumber + '\'';
        }
        claimquery += ' order by CreatedDate  desc limit 9000';
        claimList = Database.query(claimquery);
        List<WarrantyClaimListEntity> claimItemListEntityList = new List<WarrantyClaimListEntity>();
        Set<String> crmIds = new Set<String>();
        Map<String, Invoice__c> crmMap = new Map<String, Invoice__c>();
        if (claimList.size() > 0){

            Map<Id, String> claimRejectCommentsMap = getClaimRejectCommentsMap(claimList);

            for (Warranty_Claim__c claimItem : claimList){
                if (String.isNotBlank(claimItem.CRM_Order_Number__c)){
                    crmIds.add(claimItem.CRM_Order_Number__c);
                } else{
                    crmIds.add(claimItem.Id);
                }
            }
            List<Invoice__c> invoiceList = [select id, name, Claim_CRM_Id__c
                                            from Invoice__c
                                            where Claim_CRM_Id__c in:crmIds and Claim_CRM_Id__c <> null];
            for (Invoice__c invoice : invoiceList){
                crmMap.put(invoice.Claim_CRM_Id__c, invoice);
            }
            Set<String> poIdSet = new Set<String>();
            Integer totalSize = claimList.size();
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                WarrantyClaimListEntity entity = new WarrantyClaimListEntity();
                entity.claimId = claimList[i].Id;
                entity.claimName = claimList[i].Name;
                entity.companyName = claimList[i].Consumer__r.Consumer_Company_Formula__c;
                entity.claimStatus = claimList[i].Claim_Status__c;
                entity.paymentStatus = claimList[i].Payment_Status__c;
                entity.serialNumber = claimList[i].Serial_Number__c;
                entity.modelNumber = claimList[i].Model_Number__c;
                entity.laborCost = claimList[i].Labor_Final_Cost__c;
                entity.materialCost = claimList[i].Material_Actual_Cost__c;
                entity.Total = claimList[i].Total_Actual_To_Oracle__c;
                entity.currencyIsoCode = claimList[i].CurrencyIsoCode;
                entity.NameId = claimList[i].Id;
                entity.CompanyId = claimList[i].Consumer__c;
                entity.CustomerId = claimList[i].Dealer_Name__c;
                if (crmMap.containsKey(claimList[i].Id)){
                    entity.creditMemoName = crmMap.get(claimList[i].Name).name;
                    entity.creditMemoId = crmMap.get(claimList[i].Name).Id;
                } else if (String.isNotBlank(claimList[i].CRM_Order_Number__c) && crmMap.containsKey(claimList[i].CRM_Order_Number__c)){
                    entity.creditMemoName = crmMap.get(claimList[i].CRM_Order_Number__c).name;
                    entity.creditMemoId = crmMap.get(claimList[i].CRM_Order_Number__c).Id;
                } else{
                    entity.creditMemoName = '';
                    entity.creditMemoId = '';
                }
                if (String.isNotBlank(claimList[i].Warranty_Purchase_Order__c)){
                    poIdSet.add(claimList[i].Warranty_Purchase_Order__c);
                }
                entity.warrantyOrderName = claimList[i].Warranty_Purchase_Order__r.Name;
                entity.warrantyOrderId = claimList[i].Warranty_Purchase_Order__c;
                if(claimRejectCommentsMap.containsKey(claimList[i].Id)) {
                    entity.rejectComments = claimRejectCommentsMap.get(claimList[i].Id);
                }
                entity.claimReferenceNumber = claimList[i].Customer_Claim_Reference_Number__c;
                claimItemListEntityList.add(entity);
            }
            //查询orderMap

            List<Order> orderList = [select Id, PO_No__c, Order_Number__c, Warranty_Purchase_Order__c
                                     from order
                                     where Warranty_Purchase_Order__c in:poIdSet];
            for (Order order : orderList){
                orderMap.put(order.Warranty_Purchase_Order__c, order);
            }
            for (WarrantyClaimListEntity entity : claimItemListEntityList){
                if (String.isNotBlank(entity.warrantyOrderId) && orderMap.containsKey(entity.warrantyOrderId)){
                    Order orderItem = orderMap.get(entity.warrantyOrderId);
                    entity.warrantyOrderId = orderItem.Id;
                    entity.warrantyOrderName = orderItem.Order_Number__c;
                    System.debug('msg:' + orderItem);
                }
            }
        }
        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('isSuccess', 'True');
        returnMap.put('Data', claimItemListEntityList);
        returnMap.put('TotalSize', claimList.size());
        return JSON.serialize(returnMap);
    }

    private static Map<Id, String> getClaimRejectCommentsMap(List<Warranty_Claim__c> claims) {
        Map<Id, String> claimRejectCommentsMap = new Map<Id, String>();
        ProcessInstance[] piList = [Select ID, Status, TargetObject.Name, TargetObjectID,
                                    (SELECT Id, StepStatus, Comments, originalActor.FirstName,originalActor.LastName, CreatedDate FROM Steps WHERE StepStatus = 'Rejected' ORDER BY CreatedDate ASC) From ProcessInstance
                                    Where TargetObjectID IN :claims AND Status = 'Rejected'];
        for(ProcessInstance pi : piList){
            for(ProcessInstanceStep pis : pi.Steps){
                if(pis.StepStatus == 'Rejected') {
                    claimRejectCOmmentsMap.put(pi.TargetObjectID, pis.Comments);
                }
            }
        }
        return claimRejectCommentsMap;
    }
    public static String queryClaimInfoMation(String accountId){
        List<AggregateResult> claimList = new List<AggregateResult>();
        Map<String, Integer> claimInfoMap = new Map<String, Integer>();
        claimInfoMap.put('Draft', 0);
        claimInfoMap.put('Submitted', 0);
        claimInfoMap.put('Approved', 0);
        claimInfoMap.put('Rejected', 0);
        String claimquery = '';
        if (String.isBlank(accountId)){
            claimList = [select count(id), Claim_Status__c
                         from Warranty_Claim__c
                         where Claim_Status__c <> null
                         group by Claim_Status__c];
        } else{
            claimList = [select count(id), Claim_Status__c
                         from Warranty_Claim__c
                         where Claim_Status__c <> null and Dealer_Name__c = :accountId
                         group by Claim_Status__c];
        }
        if (claimList.size() > 0){
            for (AggregateResult item : claimList){
                if (claimInfoMap.containsKey(String.valueOf(item.get('Claim_Status__c')))){
                    claimInfoMap.put(String.valueOf(item.get('Claim_Status__c')), Integer.valueOf(item.get('expr0')));
                }
            }
        }
        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('isSuccess', 'True');
        returnMap.put('claimInformation', JSON.serialize(claimInfoMap));
        return JSON.serialize(returnMap);
    }
    public class WarrantyClaimListEntity{
        public String claimId;
        public String claimName;
        public String claimStatus;
        public String paymentStatus;
        public String serialNumber;
        public String modelNumber;
        public Decimal laborCost;
        public Decimal materialCost;
        public Decimal Total;
        public String creditMemoName;
        public String creditMemoId;
        public String warrantyOrderName;
        public String warrantyOrderId;
        public String currencyIsoCode;
        //新增 CompanyName
        public String CompanyName;
        //跳转Id
        public String NameId;
        public String CompanyId;
        public String CustomerId;
        // Reject comments
        public String rejectComments;
        public String claimReferenceNumber;
    }
    public class WarrantyClaimEntity{
        //basic Information
        public String claimStatus;
        public String paymentStatus;
        public String emailAddress;
        public Date dropOffDate;
        public Date repairDate;
        public String customerClaimReferenceNumber;
        //product
        public String productName;
        public String brand;
        public String serialNumber;
        public String modelNumber;
        public String userType;
        public String productId;
        public String consumerId;
        public String warrantyId;
        public String receiptLink;
        public String placeOfPurchase;
        public Date purchaseDate;
        public Date expiredDate;
        public String receiptName;
        public Boolean receiptLost;
        public String basicPermission;
        //service information
        public String serviceOption;
        public String replacementOption;
        public String repairType;
        public String description;
        public String failureCode;
        //repair part
        public List<WarrantyClaimItemEntity> partList;
        //address
        public String billAddressName;
        public String billAddressId;
        public String shipAddressName;
        public String shipAddressId;
        //additional
        public String actualTime;//hour
        public String Explanation;
        //all
        public String laborRate;
        public String standardPartsHour;//保存的所有part之和的hours
        public String totalLaborHours;//显示的hour,formula字段
        public Decimal laborCost;//货币
        public Decimal materialCost;//货币
        public Decimal Total;//货币
        public String CustomerId;
        public String DistributorOrDealer;
        public String currencyIsoCode;
        public String projectId;
        public String projectCode;
        //warranty order
        public String warrantyOrderName;
        public String warrantyOrderId;
        //new sn
        public String NewSerialNumber;
        // Reject comments
        public String rejectComments;
        public String warrantyStatus;
        public Boolean isMassUpload;
        public Decimal diagnosticFee;
    }
    public class WarrantyClaimItemEntity{
        public String claimItemId;
        public String partId;
        public String partNumber;
        public String partName;
        public String quantity;
        public Decimal unitPrice;
        public Decimal total;
        public String laborTime;//hour
        //类型:repairPart|additionalPart/masterProduct
        public String type;
        public Boolean noCalculate;
        public WarrantyClaimItemEntity(){
            noCalculate = false;
        }
    }
}