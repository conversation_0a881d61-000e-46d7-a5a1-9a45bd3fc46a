/**************************************************************************************************
 * Name: WarrantyClaimBasicHandler
 * Object: warrantyclaim
 * Purpose:  Warranty Claim basic Information
 * Author:Aria W Zhong
 * Create Date: 2023-09-25
 * Modify History:
 **************************************************************************************************/
public without sharing class WarrantyClaimBasicHandler{
    /** 保存在for a free时新增sn的方法 */
    public static String saveNewSN(String calimId, String serialNumber){
        Map<String, String> returnMap = new Map<String, String>();
        try{
            Warranty_Claim__c claim = new Warranty_Claim__c(
                Id = calimId, 
                New_Serial_Number__c = serialNumber
            );
            update claim;
            returnMap.put('isSuccess', 'true');

        } catch (Exception e){
            returnMap.put('isSuccess', 'fail');
            returnMap.put('message', e.getMessage());

        }

        return JSON.serialize(returnMap);
    }
    /** 获取 对应地址 */
    public static String queryAddress(String AccountId, String type, String name, Integer pageNumber, Integer allPageSize){
        name = '%' + name + '%';
        List<Account_Address__c> addressList = new List<Account_Address__c>();
        if (type == 'bill'){
            addressList = [select id, Name, Primary__c
                           from Account_Address__c
                           where Customer__c = :AccountId and RecordType.DeveloperName in ('Billing_Address') and Name like:name];

        }
        if (type == 'ship'){
            addressList = [select id, Name
                           from Account_Address__c 
                           where Customer__c = :AccountId and RecordType.DeveloperName in ('Shipping_Address', 'Dropship_Shipping_Address') and Name like:name];

        }
        List<AddressEntity> addressListEntityList = new List<AddressEntity>();
        AddressEntity billDefalutentity = new AddressEntity();
        if (addressList.size() > 0){
            Integer totalSize = addressList.size();
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                AddressEntity entity = new AddressEntity();
                entity.name = addressList[i].Name;
                entity.addressId = addressList[i].Id;
                addressListEntityList.add(entity);
            }
            //获取bill的默认值
            if (type == 'bill'){
                for (Account_Address__c address : addressList){
                    if (address.Primary__c = true){
                        billDefalutentity.name = address.Name;
                        billDefalutentity.addressId = address.Id;
                        break;
                    }
                }
                if (String.isBlank(billDefalutentity.addressId)){
                    billDefalutentity.name = addressList[0].Name;
                    billDefalutentity.addressId = addressList[0].Id;
                }
            }
        }
        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('List', addressListEntityList);
        returnMap.put('Type', type);
        returnMap.put('BillDefault', billDefalutentity);
        returnMap.put('TotalSize', addressList.size());
        return JSON.serialize(returnMap);
    }
    /**查询对应产品下的parts */
    public static String queryParts(String productId, Integer pageNumber, Integer allPageSize, String customerId, String name, List<String> partIds){
        String sql = 'select id, Parts__c, Parts__r.Order_Model__c, Parts__r.Item_Description_EN_Formula__c, Parts__r.Item_Description_DE_Formula__c,EstimatedRepairTime__c from Kit_Item__c where RecordType.DeveloperName in (\'Products_and_Parts\', \'Accessories_and_Parts\') and (Accessory_SP__c =\'' + productId + '\' Or Product__c = \'' + productId + '\')';
        if (String.isNotBlank(name) ){
            sql += ' and (Parts__r.Item_Description_EN_Formula__c like \'%' + name + '%\' OR Parts__r.Item_Description_DE_Formula__c like\'%' + name + '%\' OR Parts__r.Order_Model__c like \'%' + name + '%\')';
        }

        if(partIds != null && !partIds.isEmpty()) {
            sql += ' and Parts__c IN :partIds';
        }

        List<Kit_Item__c> productList = Database.query(sql);
        List<ProductEntity> productEntityList = new List<ProductEntity>();
        Boolean isDe = UserInfo.getLanguage() == 'de' ? true : false;
        if (productList.size() > 0){
            //获取产品Id
            List<String> lstProductIds = new List<String>();
            List<String> lstProducts = new List<String>();
            Map<String, Double> mapProdictId2Qty = new Map<String, Double>();

            Integer totalSize = productList.size();
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                lstProductIds.add(productList[i].Parts__c);
                mapProdictId2Qty.put(productList[i].Parts__c, 1);
                ProductEntity entity = new ProductEntity();
                lstProducts.add(productList[i].Parts__r.Order_Model__c);
                entity.modelNumber = productList[i].Parts__r.Order_Model__c;
                entity.productId = productList[i].Parts__c;
                entity.productName = isDe ? productList[i].Parts__r.Item_Description_DE_Formula__c : productList[i].Parts__r.Item_Description_EN_Formula__c;
                entity.laborTime = String.valueOf(productList[i].EstimatedRepairTime__c);
                productEntityList.add(entity);
            }
            system.debug('lstProducts:' + lstProducts);
            //获取对应的价格
            Map<String, Map<String, Object>> mapProductId2Values = new Map<String, Map<String, Object>>();
            mapProductId2Values = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfoByDownload(customerId, lstProductIds, Date.today(), mapProdictId2Qty);
            for (ProductEntity entity : productEntityList){
                Map<String, Object> mapFeild2Value = mapProductId2Values.get(entity.productId);
                system.debug('mapFeild2Value--->' + mapFeild2Value);
                if (mapFeild2Value != null){
                    entity.unitPrice = String.valueOf(((Decimal) mapFeild2Value.get(CCM_Constants.FINAL_PRICE)).setScale(2, RoundingMode.HALF_UP));
                    entity.noCalculate = false;
                } else{
                    entity.unitPrice = '0';
                    entity.noCalculate = true;
                }
            }
        }
        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('List', productEntityList);
        returnMap.put('TotalSize', productList.size());
        return JSON.serialize(returnMap);
    }
    /**查询orderModel */
    public static String queryModelNumber(String accId, string orderModel, Integer pageNumber, Integer allPageSize){
        orderModel = '%' + orderModel + '%';
        //查询是否有对应pricebook
        Account acc = new Account(
        );
        if (String.isNotBlank(accId)){
            acc = [select Id, Name, List_Price__c
                   from Account
                   where Id = :accId];
        }
        List<Pricebook_Entry__c> bookEntryList = new List<Pricebook_Entry__c>();
        List<ProductEntity> productEntityList = new List<ProductEntity>();
        //放开对account的限制
        List<Product2> newbookEntryList = new List<Product2>();
        newbookEntryList = [select id, Order_Model__c, Item_Description_EN_Formula__c, Item_Description_DE_Formula__c
                            from Product2
                            where Order_Model__c Like:orderModel and RecordType.DeveloperName in ('TLS_Product', 'ACC')
                            order by Order_Model__c Asc];
        System.debug('size' + newbookEntryList.size());
        // if (String.isNotBlank(acc.List_Price__c)){
        // bookEntryList = [select id, Product__r.Order_Model__c, Product__r.Item_Description_EN_Formula__c, Product__c
        //                  from Pricebook_Entry__c
        //                  where isActive__c = true and Start_Date__c <= :Date.today() and End_Date__c >= :Date.today() and PriceBook__c = :acc.List_Price__c and Product__r.Order_Model__c Like:orderModel
        //                  order by Product__r.Order_Model__c Asc];
        //暂时放开限制
        // if (bookEntryList.size() > 0){
        //     //获取产品Id
        //     List<String> lstProductIds = new List<String>();
        //     List<String> lstProducts = new List<String>();
        //     Map<String, Double> mapProdictId2Qty = new Map<String, Double>();

        //     Integer totalSize = bookEntryList.size();
        //     Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
        //     Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
        //     for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
        //         lstProductIds.add(bookEntryList[i].Product__c);
        //         lstProducts.add(bookEntryList[i].Product__r.Order_Model__c);
        //         mapProdictId2Qty.put(bookEntryList[i].Product__c, 1);
        //         ProductEntity entity = new ProductEntity();
        //         entity.modelNumber = bookEntryList[i].Product__r.Order_Model__c;
        //         entity.productId = bookEntryList[i].Product__c;
        //         entity.productName = bookEntryList[i].Product__r.Item_Description_EN_Formula__c;
        //         entity.laborTime = '';//不需要
        //         productEntityList.add(entity);
        //     }
        //     system.debug('lstProducts:' + lstProducts);
        //     //获取对应的价格
        //     Map<String, Map<String, Object>> mapProductId2Values = new Map<String, Map<String, Object>>();
        //     mapProductId2Values = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfoByDownload(accId, lstProductIds, Date.today(), mapProdictId2Qty);
        //     for (ProductEntity entity : productEntityList){
        //         Map<String, Object> mapFeild2Value = mapProductId2Values.get(entity.productId);
        //         system.debug('mapFeild2Value--->' + mapFeild2Value);
        //         if (mapFeild2Value != null){
        //             entity.unitPrice = String.valueOf(((Decimal) mapFeild2Value.get(CCM_Constants.FINAL_PRICE)).setScale(2, RoundingMode.HALF_UP));
        //             entity.noCalculate = false;
        //         } else{
        //             entity.unitPrice = '0';
        //             entity.noCalculate = true;
        //         }
        //     }
        // }
        // }
        if (newbookEntryList.size() > 0){
            Boolean isDe = UserInfo.getLanguage() == 'de';
            //获取产品Id
            List<String> lstProductIds = new List<String>();
            List<String> lstProducts = new List<String>();
            Map<String, Double> mapProdictId2Qty = new Map<String, Double>();

            Integer totalSize = newbookEntryList.size();
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                lstProductIds.add(newbookEntryList[i].Id);
                lstProducts.add(newbookEntryList[i].Order_Model__c);
                mapProdictId2Qty.put(newbookEntryList[i].Id, 1);
                ProductEntity entity = new ProductEntity();
                entity.modelNumber = newbookEntryList[i].Order_Model__c;
                entity.productId = newbookEntryList[i].Id;
                entity.productName = isDe ? newbookEntryList[i].Item_Description_DE_Formula__c : newbookEntryList[i].Item_Description_EN_Formula__c;
                entity.laborTime = '';//不需要
                productEntityList.add(entity);
            }
            system.debug('lstProducts:' + lstProducts);
            //获取对应的价格
            Map<String, Map<String, Object>> mapProductId2Values = new Map<String, Map<String, Object>>();
            mapProductId2Values = CCM_PurchaseBatchUploadController.CaulateProduct2PriceInfoByDownload(accId, lstProductIds, Date.today(), mapProdictId2Qty);
            for (ProductEntity entity : productEntityList){
                Map<String, Object> mapFeild2Value = mapProductId2Values.get(entity.productId);
                system.debug('mapFeild2Value--->' + mapFeild2Value);
                if (mapFeild2Value != null){
                    entity.unitPrice = String.valueOf(((Decimal) mapFeild2Value.get(CCM_Constants.FINAL_PRICE)).setScale(2, RoundingMode.HALF_UP));
                    entity.noCalculate = false;
                } else{
                    entity.unitPrice = '0';
                    entity.noCalculate = true;
                }
            }
        }

        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('List', productEntityList);
        //  returnMap.put('TotalSize', bookEntryList.size());
        returnMap.put('TotalSize', newbookEntryList.size());
        return JSON.serialize(returnMap);
    }
    /**查询orderModel不关联Customer */
    public static String queryModelNumberWithoutCustomer(string orderModel, Integer pageNumber, Integer allPageSize){
        orderModel = '%' + orderModel + '%';
        List<ProductEntity> productEntityList = new List<ProductEntity>();
        List<Product2> newbookEntryList = new List<Product2>();
        newbookEntryList = [select id, Order_Model__c, Item_Description_EN_Formula__c
                            from Product2
                            where Order_Model__c Like:orderModel
                            order by Order_Model__c Asc];
        System.debug('size' + newbookEntryList.size());
        if (newbookEntryList.size() > 0){
            //获取产品Id
            List<String> lstProductIds = new List<String>();
            List<String> lstProducts = new List<String>();
            Integer totalSize = newbookEntryList.size();
            Integer pageLength = (pageNumber - 1) * allPageSize + allPageSize - 1 < totalSize ? (pageNumber - 1) * allPageSize + allPageSize - 1:totalSize - 1;
            Integer initPageNumber = (pageNumber - 1) * allPageSize < totalSize ? (pageNumber - 1) * allPageSize : 1;
            for (Integer i = (pageNumber - 1) * allPageSize; i <= pageLength; i++){
                ProductEntity entity = new ProductEntity();
                entity.modelNumber = newbookEntryList[i].Order_Model__c;
                entity.productId = newbookEntryList[i].Id;
                entity.productName = newbookEntryList[i].Item_Description_EN_Formula__c;
                entity.laborTime = '';//不需要
                productEntityList.add(entity);
            }
            system.debug('lstProducts:' + lstProducts);
        }

        Map<String, Object> returnMap = new Map<String, Object>();
        returnMap.put('List', productEntityList);
        returnMap.put('TotalSize', newbookEntryList.size());
        return JSON.serialize(returnMap);
    }
    /**保存发票 */
    public static String saveInvoice(String idStr, String receiptType, String receiptName, String receiptBody, String warrantyItemId){
        AuraResponseEntity entity = CCM_ProductRegistration.UploadReceiptToAws(idStr, receiptType, receiptName, receiptBody);
        if (entity.code <> 201){
            Warranty_Item__c item = new Warranty_Item__c(
                Id = warrantyItemId, 
                Receipt_Link__c = entity.message
            );
            update item;
        }
        Map<String, String> returnMap = new Map<String, String>();
        returnMap.put('isSuccess', 'True');
        return JSON.serialize(returnMap);
    }
    /**检查 basic Information */
    public static String checkBasicInformation(String emailAddress, String dropDate, String repairDate, String brand, String serialNumber, String modelnumber, String accId){
        Boolean flag = true;
        List<String> errList = new List<String>();
        //校验
        if (String.isBlank(emailAddress) || String.isBlank(serialNumber)){
            errList.add(Label.Email_Or_SN_Miss);
        }
        //校验SN
        if (string.isNotBlank(serialNumber)){
            if (serialNumber.substring(0, 1) != 'E'){
                errList.add('SN-' + serialNumber + Label.SN_Check1);
            } else if (serialNumber.length() != 15){
                errList.add('SN-' + serialNumber + Label.SN_Check2);
            } else{
                //2-5位校验
                String modelCodeSN = String.isNotBlank(serialNumber) ? serialNumber.substring(1, 5) : '';
                system.debug(modelnumber + ' ' + modelCodeSN);
                for (Warranty_Rules__c warrantyRule : [SELECT Id, Product_Model__c, Code_in_Serial__c
                                                       FROM Warranty_Rules__c
                                                       WHERE Product_Model__c = :modelnumber AND RecordType.developerName = 'Model_Code' AND Product_Model__c != null AND Code_in_Serial__c != null]){
                    if (warrantyRule.Code_in_Serial__c <> modelCodeSN){
                        errList.add('SN-' + serialNumber + Label.SN_Check3);
                    }
                }
                //3.6-9位:时间数据校验
                //4.10-14位数字校验
                if (!serialNumber.substring(9, 14).isNumeric()){
                    errList.add('SN-' + serialNumber + Label.SN_Check4);
                }
                //5.15位大写字母校验
                Set<String> endCodeSet = new Set<String>{ 'X', 'A', 'B' };
                if (!endCodeSet.contains(serialNumber.right(1))){
                    errList.add('SN-' + serialNumber + Label.SN_Check5);
                }
            }
        }
        Map<String, Object> returnMap = new Map<String, Object>();
        if (errList.size() > 0){
            returnMap.put('isSuccess', 'False');
        } else{
            returnMap.put('isSuccess', 'True');
        }
        returnMap.put('message', errList);
        return JSON.serialize(returnMap);
    }
    public static Boolean checkDateFormt(String dateString){
        try{
            Date FormulaDate = Date.valueOf(dateString);
            return true;
        } catch (Exception e){
            system.debug('时间格式');
            return false;
        }
    }
    /**查询发票 */
    public static String queryInvoice(String modelNumber, String serialNumber, String accId, String email){
        Boolean flag = true;
        List<String> errList = new List<String>();
        //查询warranty item
        List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();
        if (String.isNotBlank(serialNumber)){
            wiList = [select id, Consumer__r.PersonEmail, Warranty_Status__c, PURCHASE_DATE__C, Expiration_Date_New__c, Purchase_Place__c, Receipt_Lost__c, Product__c, Consumer__c, Consumer__r.Name, Receipt_Link__c, Product__r.Order_Model__c, Product_Name__c
                      from Warranty_Item__c
                      where Serial_Number__c = :serialNumber and Product__r.Order_Model__c = :modelNumber
                      ORDER BY CreatedDate DESC
                      Limit 1];
        }
        List<ReturnEntity> entityList = new List<ReturnEntity>();
        if (wiList.size() > 0){
            for (Warranty_Item__c item : wiList){
                ReturnEntity entity = new ReturnEntity();
                entity.warrantyId = item.Id;
                entity.consumerId = item.Consumer__c;
                entity.productId = item.Product__c;
                entity.cosumerName = item.Consumer__r.Name;
                entity.productModel = item.Product__r.Order_Model__c;
                entity.receiptLink = item.Receipt_Link__c;
                entity.placeOfPurchase = item.Purchase_Place__c;
                entity.purchaseDate = item.Purchase_Date__c;
                entity.expiredDate = item.Expiration_Date_New__c;
                entity.receiptLost = item.Receipt_Lost__c;
                entity.email = item.Consumer__r.PersonEmail;
                entity.warrantyStatus = item.Warranty_Status__c;
                if (String.isNotBlank(item.Receipt_Link__c)){
                    List<String> receiptList = item.Receipt_Link__c.split('/');
                    entity.receiptName = receiptList.get(receiptList.size() - 1);
                }
                //查看权限判断
                List<Dealer_Share__c> shareList = [select id, Permission__c, RelatedConsumer__c, RelatedDealer__c
                                                   from Dealer_Share__c
                                                   WHERE RelatedConsumer__c = :item.Consumer__c and RelatedDealer__c = :accId];
                if (shareList.size() > 0){
                    entity.basicPermission = shareList.get(0).Permission__c;
                } else if (item.Consumer__c == accId){
                    entity.basicPermission = 'View';
                } else{
                    entity.basicPermission = 'Null';
                }
                //email check
                if (String.isNotBlank(email) && email <> item.Consumer__r.PersonEmail){
                    entity.emailCheck = false;
                } else{
                    entity.emailCheck = true;
                }

                entityList.add(entity);
            }
        } else{
            errList.add(Label.SN_not_Match);
        }

        Map<String, Object> returnMap = new Map<String, Object>();
        if (errList.size() > 0){
            returnMap.put('isSuccess', 'False');
        } else{
            returnMap.put('isSuccess', 'True');
        }
        returnMap.put('message', errList);
        returnMap.put('data', entityList);
        return JSON.serialize(returnMap);
    }
    public class AddressEntity{
        public String addressId;
        public String name;
    }
    public class ProductEntity{
        public String productId;
        public String modelNumber;
        public String productName;
        public String unitPrice;
        public String laborTime;//以分钟为单位
        public Boolean noCalculate;//没有被计算,默认false
    }
    public class ReturnEntity{
        public String warrantyId;
        public String consumerId;
        public String productId;
        public String cosumerName;
        public String productModel;
        public String receiptLink;
        public String placeOfPurchase;
        public Date purchaseDate;
        public Date expiredDate;
        public String receiptName;
        public Boolean receiptLost;
        public String basicPermission;
        public String email;
        //true为email check成功
        public Boolean emailCheck;
        public String warrantyStatus;
    }
}