({
    applyTrainingCourse : function(component, event, helper) {
        var action = component.get("c.queryCourseArrangementList");
        action.setParams({
            courseSettingId: component.get('v.trainingid'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log('state--->>>>>>>>>',state,state == "SUCCESS");
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                console.log("results",results);
                console.log("results",'111');
                if(results.lstArrangemtInfo){
                    // 拼接开始&结束时间,若无,置空
                    results.lstArrangemtInfo.forEach(item => {
                        item.arrangementStartTime = item.arrangementDate ? item.arrangementDate + " " + item.arrangementStartTime : ""
                        item.arrangementendTime = item.arrangementDate ? item.arrangementDate + " " + item.arrangementendTime : ""
                        item.disableBtn = item.arrangementSlot ? (item.arrangementSlot[0] == "0" ? true : false) : false
                    });
                }else{
                    component.set('v.hasArrangement',false)
                    
                }
                console.log("results",JSON.stringify(results));
                component.set('v.TrainingCourseInfo',results)
            }
        });
        $A.enqueueAction(action);
    },
    queryCourseArrangementInfo : function(component) {
        var action = component.get("c.queryCourseArrangementInfo");
        action.setParams({
            courseArrangementId: component.get("v.ArrangementId"),
            // courseArrangementId: "a1j7Y000004WVe9QAG",
            customerId: component.get("v.customerId")
        });
        action.setCallback(this, function (response) {
            component.set('v.isBusy',false)
            var state = response.getState();
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                console.log(JSON.stringify(results),"点击doRegister获得的数据");
                component.set('v.CoursePrice',results.price)
                if(component.get('v.BillToAddress') && component.get('v.BillToAddress').country == 'DE'){
                    component.set('v.VAT',component.get("v.CoursePrice") * 0.19)
                }else{
                    component.set('v.VAT',0)
                }
                console.log("CoursePrice",results.price);
                console.log("VAT",component.get('v.VAT'));
                component.set('v.paymentTerm',results.paymentTerm)
            }
        });
        $A.enqueueAction(action);
    },
    // 获取customerid
    queryRegisterList : function(component, event, helper) {
        var action = component.get("c.GetCurrentUserCustomer");
        action.setParams({});
        action.setCallback(this, function (response) {
            const state = response.getState();
            if (state === "SUCCESS") {
                const res = response.getReturnValue();
                console.log(JSON.stringify(res), '获取到的Customer信息');
                component.set('v.customerId',res.CustomerId)
                component.set('v.CustomerName',res.CustomerName)
                component.set('v.currencySymbol',res.CurrencyIsoCode)
                if(res.isDealer === 'True') {
                    component.set('v.isDealer', true);
                }
            } else {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert("ERROR: " + errors[0].message);
                    }
                } else {
                    alert("ERROR: Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    // 最终的submit
    RegisterSubmit : function(component) {
        console.log("RegisterId",component.get('v.RegisterId'));
        var action = component.get("c.RegisterSubmit");
        action.setParams({
            JsonApprovalInfoString: JSON.stringify({
                "recordId":component.get('v.RegisterId'),
                "comments":""
                })
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            var results = response.getReturnValue();
            console.log(state,"state");
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                if(results == 'SUCCESS'){
                    console.log(JSON.parse(JSON.stringify(results)),"最终提交");
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Success"),
                        "message": $A.get("$Label.c.CCM_RequestSubmitSuccess"),
                        "type": "success",
                    }).fire();
                    setTimeout(() => {
                        var url = '/s/training-detail-page?0.recordId=' + component.get('v.RegisterId');
                        window.open(url,"_self");
                    }, 1000);
                }else{
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Error"),
                        "message": results,
                        "type": "error",
                    }).fire();

                }

                
            }
        });
        component.set('v.isBusy',false)
        $A.enqueueAction(action);
    },
    // 保存当前的编辑信息
    upsertRegisterInfo : function(component,pcs,saveasdraft,saveOnly) {
        // 回填的时候的数据
        let _jsonInfo = {
            customerObj : component.get('v.customerObj'),
            BillToAddress : component.get('v.BillToAddress'),
            ShipToAddress : component.get('v.ShipToAddress'),
            detailInfo : component.get('v.detailInfo'),
            TrainingCourseList : component.get('v.TrainingCourseList'),
            TrainingCourseInfo : component.get('v.TrainingCourseInfo')
        }
        let _lstparticipants = []
        component.get('v.pcsList').forEach(item =>{
            _lstparticipants.push({
                participants:item.participants,
                SMS:item.SMS,
                email:item.email,
                remark:item.remark,
            })
        })
        var action = component.get("c.upsertRegisterInfo");
        action.setParams({
            jsonData: JSON.stringify({
                "jsonInfo":                 JSON.stringify(_jsonInfo),
                "courseArrangementId":      component.get("v.ArrangementId"),
                "paymentTerm":              component.get('v.paymentTerm'),
                "billAddressId":            component.get('v.BillToAddress').id,
                "shipAddressId":            component.get('v.ShipToAddress').id,
                "customerId":               component.get('v.customerId'),
                "pcs":                      pcs,
                "trainingCourseId":         component.get('v.TrainingCourseInfo').courseId,
                "courseRegisterId":         component.get('v.RegisterId') || ""          //更新的时候传入
            }),
            lstparticipants:JSON.stringify(_lstparticipants)
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            var results = response.getReturnValue();
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                if(results == "The maximum number of seats is exceeded"){
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": results,
                    "type": "error",
                }).fire();
                }else if(saveasdraft){
                    window.open('/s/Training','_self')
                }else if(saveOnly){
                    var toastEvt = $A.get("e.force:showToast");
                    toastEvt.setParams({
                        "title": $A.get("$Label.c.CCM_Success"),
                        "message": $A.get("$Label.c.CCM_RequestSaveSuccess"),
                        "type": "success",
                    }).fire();
                }else{
                    console.log("results",JSON.stringify(results));
                    component.set('v.RegisterId',results)
                    component.set('v.currentStep',3)
                }
                component.set('v.isBusy',false)
                // this.RegisterSubmit(component)
                // var toastEvt = $A.get("e.force:showToast");
                // toastEvt.setParams({
                //     "title": $A.get("$Label.c.CCM_Success"),
                //     "message": "success",
                //     "type": "success",
                // }).fire();
                // setTimeout(() => {
                //     var url = '/s/training-detail-page?0.recordId=' + results;
                //     window.open(url,"_self");
                // }, 1000);
            }
        });
        $A.enqueueAction(action);
    },
    // 获取信息回填
    queryRegisterInfo : function(component) {
        var action = component.get("c.queryRegisterInfo");
        action.setParams({
            "registerId":component.get('v.RegisterId'),
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                let jsonInfo = JSON.parse(results.jsonInfo)
                console.log("results",JSON.stringify(results),"jsonInfo",JSON.stringify(jsonInfo));
                jsonInfo.customerObj ? component.set('v.customerObj',jsonInfo.customerObj) : component.set('v.customerObj',{
                    customerId:results.customerId,
                    name:results.customerName,
                    accountNumber:results.customerNumber,
                    Name:results.customerName,
                    Id:results.customerId,
                })
                jsonInfo.BillToAddress ? component.set('v.BillToAddress',jsonInfo.BillToAddress) : ""
                jsonInfo.ShipToAddress ? component.set('v.ShipToAddress',jsonInfo.ShipToAddress) : ""
                jsonInfo.detailInfo ? component.set('v.detailInfo',jsonInfo.detailInfo) : ""
                jsonInfo.TrainingCourseList ? component.set('v.TrainingCourseList',jsonInfo.TrainingCourseList) : ""
                jsonInfo.TrainingCourseInfo ? component.set('v.TrainingCourseInfo',jsonInfo.TrainingCourseInfo) : ""
                component.set('v.trainingid',component.get('v.TrainingCourseInfo').courseId)
                component.set('v.ArrangementId',results.arrangementId)
                this.applyTrainingCourse(component)
                component.set('v.pcs',results.pcs)
                if(results.lstparticipants.length){
                    console.log(JSON.stringify({
                        trainee : results.lstparticipants[0].trainee,
                        sms : results.lstparticipants[0].sms,
                        email : results.lstparticipants[0].email,
                        remark : results.lstparticipants[0].remark
                    }),"pcsList");
                    var pcsList = []
                    for (let i = 0; i < results.pcs; i++) {
                        pcsList.push({
                            "participants":      results.lstparticipants[i].trainee ? results.lstparticipants[i].trainee : "",
                            "SMS":               results.lstparticipants[i].sms     ? results.lstparticipants[i].sms     : "",
                            "email":             results.lstparticipants[i].email   ? results.lstparticipants[i].email   : "",
                            "remark":            results.lstparticipants[i].remark  ? results.lstparticipants[i].remark  : "",
                            "index":             i + 1
                        })
                    }
                }
                component.set('v.pcs',results.pcs)
                component.set('v.pcsList',pcsList)
            }
        });
        $A.enqueueAction(action);
    },
    // 提交期望时间
    upsertProposeArrangement : function(component,actionstr) {
        var action = component.get("c.upsertProposeArrangement");
        action.setParams({
            jsonData: JSON.stringify({
                "proposeId":             "",
                "proposeLocation":       component.get('v.SbumitProposeInfo').location.id,
                "description":           component.get('v.SbumitProposeInfo').description,
                "proposeStartTime":      component.get('v.SbumitProposeInfo').StartTime,
                "proposeEndTime":        component.get('v.SbumitProposeInfo').EndTime,
                "proposeDate":           component.get('v.SbumitProposeInfo').date,
                "courseSetting":         component.get('v.TrainingCourseInfo').courseId,
            })
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state == "SUCCESS") {
                var results = response.getReturnValue();
                console.log(JSON.parse(JSON.stringify(results)),"提交期望时间");
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Success"),
                    "message": $A.get("$Label.c.CCM_Success"),
                    "type": "success",
                }).fire();
                setTimeout(() => {
                    var url = '/s/Training';
                    window.open(url,"_self");
                }, 1000);
            }
        });
        $A.enqueueAction(action);
    },
})