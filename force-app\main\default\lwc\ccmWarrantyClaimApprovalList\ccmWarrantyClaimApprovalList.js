import { LightningElement } from 'lwc';
import getAllClaims from '@salesforce/apex/CCM_WarrantyClaimApprovalListCtrl.getWarrantyClaims'
import RedLight from '@salesforce/resourceUrl/RedLight';
import YellowLight from '@salesforce/resourceUrl/YellowLight';
import GreenLight from '@salesforce/resourceUrl/GreenLight';
import OrangeLight from '@salesforce/resourceUrl/OrangeLight';

export default class CcmWarrantyClaimApprovalList extends LightningElement {

    
    //Light Status
    RedLight = RedLight;
    YellowLight = YellowLight;
    GreenLight = GreenLight;
    OrangeLight = OrangeLight;
    
    columns = [
        // {label: 'id', fieldName: 'id', type: 'text', hideLabel: true},
        // {label: 'Warranty Claim Name', fieldName: 'name', type: 'text'},
        {label: 'Warranty Claim Name', fieldName: 'recordLink', type: 'url', typeAttributes:{label: { fieldName: 'name' }, target: '_blank'}},
        {label: 'Light', type: 'customImage', fieldName: 'imageUrl', typeAttributes:{imageUrl: { fieldName: 'imageUrl' }}},
        {label: 'Serial Number', fieldName: 'serialNumber', type: 'text', sortable: true},
        {label: 'Dealer Name', fieldName: 'dealerName', type: 'text', sortable: true},
        // {label: 'Light', fieldName: 'light', type: 'text'},
        {label: 'Model Number', fieldName: 'modelNumber', type: 'text', sortable: true},
        {label: 'Claim Date', fieldName: 'claimDate', type: 'date', sortable: true},
        {label: 'Service Option', fieldName: 'serviceOption', type: 'text', sortable: true},
        {label: 'Replacement Option', fieldName: 'replacementOption', type: 'text', sortable: true},
        {label: 'Repair Type', fieldName: 'repairType', type: 'text', sortable: true},
        {label: 'Create Date', fieldName: 'createdDate', type: 'date', sortable: true},
    ];

    values = [];
    
    connectedCallback() {
        this.initApprovalList();
    }

    initApprovalList() {
        getAllClaims().then(res=>{
            if(res) {
                let resJson = JSON.parse(res);
                resJson.forEach(item=>{
                    if(item.light === 'Green') {
                        item.imageUrl = GreenLight;
                    }
                    else if(item.light === 'Red') {
                        item.imageUrl = RedLight;
                    }
                    else if(item.light === 'Orange') {
                        item.imageUrl = OrangeLight;
                    }
                    else if(item.light === 'Yellow') {
                        item.imageUrl = YellowLight;
                    }
                });
                this.values = resJson;
                this.template.querySelector('c-ccm-mass-approval-list').hideLoading();
            }
        }).catch(error=>{
            console.log(error);
        });
    }

    handleApprove() {
        this.initApprovalList();
    }

    handleReject() {
        this.initApprovalList();
    }
}